# Court Crawling System - Deployment Guide

This guide provides step-by-step instructions for deploying the Court Crawling System to Google Cloud Run.

## Prerequisites

1. **Google Cloud Project** with billing enabled
2. **gcloud CLI** installed and authenticated
3. **Docker** installed (for local testing)
4. **uv** package manager installed
5. **Service Account Key** for Google Sheets access

## Step 1: Initial Setup

### 1.1 Clone and Setup Project
```bash
git clone <repository-url>
cd layer_crawling
uv sync
```

### 1.2 Set Environment Variables
```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"
export REGION="us-central1"  # or your preferred region
export NOTIFICATION_EMAIL="<EMAIL>"
```

### 1.3 Authenticate with Google Cloud
```bash
gcloud auth login
gcloud config set project $GOOGLE_CLOUD_PROJECT
```

## Step 2: Prepare Secrets

### 2.1 Create Google Sheets Service Account
1. Go to Google Cloud Console → IAM & Admin → Service Accounts
2. Create a new service account named "court-crawling-sheets"
3. Download the JSON key file as `google-sheets-credentials.json`
4. Share your Google Sheets with the service account email

### 2.2 Prepare Credential Files
Create the following files in your project root:

**lawyer-accounts.json**:
```json
{
  "accounts": [
    {
      "username": "lawyerkk94",
      "password": "kkcho0904!",
      "assigned_courts": ["서울중앙지방법원", "서울동부지방법원"],
      "active": true
    }
  ]
}
```

**electronic-litigation-credentials.json**:
```json
{
  "username": "lawyer87",
  "password": "kkcho0904!"
}
```

## Step 3: Deploy Infrastructure

### 3.1 Setup Secrets and Service Accounts
```bash
./deploy/setup-secrets.sh
```

This script will:
- Enable required Google Cloud APIs
- Create secrets in Secret Manager
- Create service account with proper permissions

### 3.2 Build and Deploy Application
```bash
./deploy/deploy.sh
```

This script will:
- Build Docker image using Cloud Build
- Deploy to Cloud Run
- Configure service settings

### 3.3 Setup Scheduled Jobs
```bash
# Update SERVICE_URL in the script with your actual Cloud Run URL
export SERVICE_URL="https://your-service-url"
export API_KEY="your-api-key"  # optional

./deploy/setup-scheduler.sh
```

### 3.4 Setup Monitoring and Alerting
```bash
./deploy/setup-monitoring.sh
```

## Step 4: Configuration

### 4.1 Environment Variables
The following environment variables are automatically set during deployment:

- `GOOGLE_CLOUD_PROJECT`: Your project ID
- `ENVIRONMENT`: Set to "production"
- `WEBDRIVER_HEADLESS`: Set to "true"
- `LOG_LEVEL`: Set to "INFO"

### 4.2 API Authentication (Optional)
To enable API key authentication:

1. Generate a secure API key:
   ```bash
   openssl rand -base64 32
   ```

2. Update the Cloud Run service with the API key:
   ```bash
   gcloud run services update court-crawling-system \
     --region=$REGION \
     --set-env-vars="API_KEY=your-generated-api-key"
   ```

## Step 5: Testing

### 5.1 Health Check
```bash
curl https://your-service-url/health
```

### 5.2 Test Crawling Endpoints
```bash
# Public Defender Crawling
curl -X POST https://your-service-url/crawl/public-defender \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"date_range_days": 7}'

# Electronic Litigation Crawling
curl -X POST https://your-service-url/crawl/electronic-litigation \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{}'

# Combined Crawling
curl -X POST https://your-service-url/crawl/all \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"date_range_days": 7}'
```

### 5.3 Test Scheduled Jobs
```bash
# Manually trigger a scheduled job
gcloud scheduler jobs run public-defender-daily-crawl --location=$REGION
```

## Step 6: Monitoring

### 6.1 View Logs
```bash
# View recent logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=court-crawling-system" --limit=50

# Follow logs in real-time
gcloud logs tail "resource.type=cloud_run_revision AND resource.labels.service_name=court-crawling-system"
```

### 6.2 Monitor Metrics
- Go to Google Cloud Console → Monitoring
- View the "Court Crawling System Dashboard"
- Check alerting policies

### 6.3 Error Reporting
- Go to Google Cloud Console → Error Reporting
- View any application errors

## Step 7: Maintenance

### 7.1 Update Application
```bash
# Make your changes, then redeploy
./deploy/deploy.sh
```

### 7.2 Update Secrets
```bash
# Update a secret
echo "new-secret-value" | gcloud secrets versions add secret-name --data-file=-
```

### 7.3 Scale Service
```bash
# Update resource limits
gcloud run services update court-crawling-system \
  --region=$REGION \
  --memory=4Gi \
  --cpu=4 \
  --max-instances=20
```

### 7.4 Pause/Resume Scheduled Jobs
```bash
# Pause all jobs
gcloud scheduler jobs pause public-defender-daily-crawl --location=$REGION
gcloud scheduler jobs pause electronic-litigation-daily-crawl --location=$REGION
gcloud scheduler jobs pause combined-weekly-crawl --location=$REGION

# Resume all jobs
gcloud scheduler jobs resume public-defender-daily-crawl --location=$REGION
gcloud scheduler jobs resume electronic-litigation-daily-crawl --location=$REGION
gcloud scheduler jobs resume combined-weekly-crawl --location=$REGION
```

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check Docker syntax
   - Verify all dependencies are in pyproject.toml
   - Check Cloud Build logs

2. **Deployment Failures**:
   - Verify service account permissions
   - Check resource quotas
   - Verify region availability

3. **Runtime Errors**:
   - Check application logs
   - Verify secrets are properly configured
   - Check Chrome/ChromeDriver compatibility

4. **Crawling Failures**:
   - Verify website accessibility
   - Check credential validity
   - Monitor for website changes

   - Check credential validity
   - Monitor for website changes

5. **Permission Denied when Pulling Docker Image Locally**:
   - **Error**: `denied: Permission "artifactregistry.repositories.downloadArtifacts" denied...`
   - **Reason**: Your user account needs permission to pull images from Artifact Registry, which now backs `gcr.io`.
   - **Solution**: Grant the `Artifact Registry Reader` role to your user account.
     ```bash
     # 1. Find your authenticated user email
     gcloud auth list

     # 2. Grant the required role (replace [YOUR_EMAIL] with the email from step 1)
     gcloud projects add-iam-policy-binding your-project-id \
       --member="user:[YOUR_EMAIL]" \
       --role="roles/artifactregistry.reader"

     # 3. Ensure Docker is configured to use gcloud credentials (run once)
     gcloud auth configure-docker
     ```


### Getting Help

1. Check application logs in Cloud Console
2. Review Error Reporting for exceptions
3. Monitor alerting policies for notifications
4. Check Cloud Run service metrics

## Security Considerations

1. **Secrets Management**: All sensitive data is stored in Secret Manager
2. **Service Account**: Minimal required permissions
3. **Network Security**: Cloud Run provides HTTPS by default
4. **API Authentication**: Optional API key protection
5. **Container Security**: Non-root user in Docker container

## Cost Optimization

1. **Scaling**: Configured with min-instances=0 for cost efficiency
2. **Resource Limits**: Right-sized memory and CPU allocation
3. **Timeout**: 15-minute timeout for long operations
4. **Monitoring**: Alerts for unusual resource usage

This completes the deployment of your Court Crawling System on Google Cloud Run!
