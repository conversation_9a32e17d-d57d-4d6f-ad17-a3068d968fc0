steps:
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '--no-cache'
      - '-t'
      - >-
        $_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
      - .
      - '-f'
      - Dockerfile
    id: Build
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - >-
        $_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
    id: Push
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:slim'
    args:
      - run
      - services
      - update
      - $_SERVICE_NAME
      - '--platform=managed'
      - >-
        --image=$_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
      - >-
        --labels=managed-by=gcp-cloud-build-deploy-cloud-run,commit-sha=$COMMIT_SHA,gcb-build-id=$BUILD_ID,gcb-trigger-id=$_TRIGGER_ID
      - '--region=$_DEPLOY_REGION'
      - '--quiet'
      - '--set-env-vars=ENVIRONMENT=$_ENVIRONMENT'
      - '--set-env-vars=DEBUG=$_DEBUG'
      - '--set-env-vars=TIMEZONE=$_TIMEZONE'
      - '--set-env-vars=GOOGLE_CLOUD_PROJECT=$_GOOGLE_CLOUD_PROJECT'
      - '--set-env-vars=WEBDRIVER_HEADLESS=$_WEBDRIVER_HEADLESS'
      - '--set-env-vars=WEBDRIVER_WINDOW_WIDTH=$_WEBDRIVER_WINDOW_WIDTH'
      - '--set-env-vars=WEBDRIVER_WINDOW_HEIGHT=$_WEBDRIVER_WINDOW_HEIGHT'
      - '--set-env-vars=WEBDRIVER_TIMEOUT_SECONDS=$_WEBDRIVER_TIMEOUT_SECONDS'
      - '--set-env-vars=PUBLIC_DEFENDER_BASE_URL=$_PUBLIC_DEFENDER_BASE_URL'
      - >-
        --set-env-vars=PUBLIC_DEFENDER_DATE_RANGE_DAYS=$_PUBLIC_DEFENDER_DATE_RANGE_DAYS
      - '--set-env-vars=PUBLIC_DEFENDER_MAX_RETRIES=$_PUBLIC_DEFENDER_MAX_RETRIES'
      - >-
        --set-env-vars=ELECTRONIC_LITIGATION_BASE_URL=$_ELECTRONIC_LITIGATION_BASE_URL
      - >-
        --set-env-vars=ELECTRONIC_LITIGATION_MAX_RETRIES=$_ELECTRONIC_LITIGATION_MAX_RETRIES
      - '--set-env-vars=GOOGLE_SHEETS_BATCH_SIZE=$_GOOGLE_SHEETS_BATCH_SIZE'
      - >-
        --set-env-vars=GOOGLE_SHEETS_RETRY_ATTEMPTS=$_GOOGLE_SHEETS_RETRY_ATTEMPTS
      - >-
        --set-env-vars=SECRET_MANAGER_LAWYER_ACCOUNTS=$_SECRET_MANAGER_LAWYER_ACCOUNTS
      - >-
        --set-env-vars=SECRET_MANAGER_ELECTRONIC_LITIGATION=$_SECRET_MANAGER_ELECTRONIC_LITIGATION
      - >-
        --set-env-vars=SECRET_MANAGER_GOOGLE_SHEETS=$_SECRET_MANAGER_GOOGLE_SHEETS
      - >-
        --set-env-vars=SECRET_MANAGER_GOOGLE_SHEETS_ELECTRONIC=$_SECRET_MANAGER_GOOGLE_SHEETS_ELECTRONIC
      - >-
        --set-env-vars=SECRET_MANAGER_GOOGLE_SHEETS_PUBLIC=$_SECRET_MANAGER_GOOGLE_SHEETS_PUBLIC
      - '--set-env-vars=SECRET_MANAGER_API_KEY=$_SECRET_MANAGER_API_KEY'
      - '--set-env-vars=LOG_LEVEL=$_LOG_LEVEL'
      - '--set-env-vars=LOG_FORMAT=$_LOG_FORMAT'
      - '--set-env-vars=API_HOST=$_API_HOST'
      - '--set-env-vars=API_PORT=$_API_PORT'
      - '--set-env-vars=HOST=$_HOST'
    id: Deploy
    entrypoint: gcloud
images:
  - >-
    $_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
options:
  substitutionOption: ALLOW_LOOSE
  logging: CLOUD_LOGGING_ONLY
substitutions:
  _GOOGLE_SHEETS_RETRY_ATTEMPTS: '5'
  _SECRET_MANAGER_GOOGLE_SHEETS_ELECTRONIC: electronic-google-sheets-credentials
  _DEBUG: 'false'
  _LOG_LEVEL: INFO
  _SERVICE_NAME: court-crawling-system
  _WEBDRIVER_TIMEOUT_SECONDS: '30'
  _PUBLIC_DEFENDER_BASE_URL: 'https://guksun.scourt.go.kr/pkj/index.on'
  _AR_PROJECT_ID: crawling-470106
  _WEBDRIVER_HEADLESS: 'false'
  _DEPLOY_REGION: asia-northeast3
  _ENVIRONMENT: development
  _AR_REPOSITORY: cloud-run-source-deploy
  _GOOGLE_CLOUD_PROJECT: crawling-law
  _ELECTRONIC_LITIGATION_MAX_RETRIES: '3'
  _API_HOST: 0.0.0.0
  _SECRET_MANAGER_GOOGLE_SHEETS_PUBLIC: public-defender-google-sheets-credentials
  _PLATFORM: managed
  _GOOGLE_SHEETS_BATCH_SIZE: '500'
  _HOST: 0.0.0.0
  _PUBLIC_DEFENDER_MAX_RETRIES: '3'
  _ELECTRONIC_LITIGATION_BASE_URL: 'https://ecfs.scourt.go.kr/psp/index.on?m=PSP101M01'
  _SECRET_MANAGER_API_KEY: webhook-api-key
  _AR_HOSTNAME: asia-northeast3-docker.pkg.dev
  _WEBDRIVER_WINDOW_HEIGHT: '1080'
  _SECRET_MANAGER_ELECTRONIC_LITIGATION: electronic-litigation-credentials
  _SECRET_MANAGER_LAWYER_ACCOUNTS: lawyer-accounts
  _TIMEZONE: Asia/Seoul
  _SECRET_MANAGER_GOOGLE_SHEETS: google-sheets-credentials
  _WEBDRIVER_WINDOW_WIDTH: '1920'
  _TRIGGER_ID: df5c049a-a9dd-42d4-918e-133421d537cb
  _API_PORT: '8080'
  _PUBLIC_DEFENDER_DATE_RANGE_DAYS: '7'
  _LOG_FORMAT: json
tags:
  - gcp-cloud-build-deploy-cloud-run
  - gcp-cloud-build-deploy-cloud-run-managed
  - court-crawling-system