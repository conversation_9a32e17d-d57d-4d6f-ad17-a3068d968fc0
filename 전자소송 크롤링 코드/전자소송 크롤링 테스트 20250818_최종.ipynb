{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7d87d631", "metadata": {}, "outputs": [], "source": ["# 최종 테스트 완료\n", "# # -*- coding: utf-8 -*-\n", "\"\"\"\n", "전자소송 크롤링 - 관심사건 일괄 수집(일반내용 전체문자열 + 진행내용)\n", "- 로그인: 탭 클릭 → ID 입력 → 가상 키패드로 비밀번호 입력 → 로그인\n", "- 일반내용: 컨테이너 탐색(JS) → 전체문자열 저장, 사건번호는 목록 a태그에서 우선 확보\n", "- 진행내용: 탭 클릭 → 테이블에서 '일자','내용','결과' 추출\n", "- 결과 저장: 구글 스프레드시트 2시트 업로드\n", "    · 사건 일반내용 크롤링 로그  : [사건번호, 전체문자열, 크롤링일시]   ← 마지막 열 자동 주입\n", "    · 사건 진행내역 크롤링 로그  : [사건번호, 일자, 내용, 결과, 크롤링일시] ← 마지막 열 자동 주입\n", "\"\"\"\n", "\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "import time, re, pyperclip, pandas as pd\n", "from datetime import datetime\n"]}, {"cell_type": "code", "execution_count": null, "id": "c5397d5f", "metadata": {}, "outputs": [], "source": ["# ===== 구글 스프레드시트 업로드 유틸 =====\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "\n", "SPREADSHEET_ID = \"1y4V6DRBobKltlc5NpRsLHN90bucEsIHqZdFm1VqeATY\"\n", "WS_PROG = \"사건 진행내역 크롤링 로그\"\n", "WS_GEN  = \"사건 일반내용 크롤링 로그\"\n", "\n", "# 업로드 템플릿 (마지막 열 '크롤링일시'는 업로드 시 자동 입력)\n", "TEMPLATE_GEN  = [\"사건번호\", \"전체문자열\", \"크롤링일시\"]\n", "TEMPLATE_PROG = [\"사건번호\", \"일자\", \"내용\", \"결과\", \"크롤링일시\"]\n", "TS_COL_NAME   = \"크롤링일시\"\n", "\n", "def _now_ts() -> str:\n", "    return datetime.now().strftime('%Y-%m-%d %H:%M')\n", "\n", "def get_client():\n", "    creds  = ServiceAccountCredentials.from_json_keyfile_name(\"credentials.json\", [\n", "        \"https://www.googleapis.com/auth/spreadsheets\",\n", "        \"https://www.googleapis.com/auth/drive\",\n", "    ])\n", "    return gspread.authorize(creds)\n", "\n", "def _open_ss_by_id(client, key: str):\n", "    try:\n", "        return client.open_by_key(key)\n", "    except gspread.SpreadsheetNotFound as e:\n", "        raise RuntimeError(\"스프레드시트를 ID로 열 수 없습니다. \"\n", "                           \"ID가 맞는지, 서비스계정이 편집자로 공유되었는지 확인하세요.\") from e\n", "\n", "def _open_or_create_ws(ss, title: str, rows=\"1000\", cols=\"26\"):\n", "    try:\n", "        return ss.worksheet(title)\n", "    except gspread.exceptions.WorksheetNotFound:\n", "        return ss.add_worksheet(title=title, rows=rows, cols=cols)\n", "\n", "def _ensure_header(ws, template_cols: list[str]):\n", "    if ws.get_all_values():\n", "        return\n", "    ws.append_row(template_cols)\n", "\n", "def _df_to_rows_with_ts(df: pd.DataFrame, template_cols: list[str]) -> list[list[str]]:\n", "    \"\"\"\n", "    템플릿 마지막 열이 TS_COL_NAME(=크롤링일시)이면 업로드 시 자동 주입.\n", "    → DF에는 '크롤링일시' 컬럼이 없어야 정상 동작(마지막 열은 우리가 채움).\n", "    \"\"\"\n", "    rows: list[list[str]] = []\n", "    is_ts_last = (template_cols and template_cols[-1] == TS_COL_NAME)\n", "    for _, r in df.iterrows():\n", "        ordered = []\n", "        for idx, col in enumerate(template_cols):\n", "            if is_ts_last and idx == len(template_cols) - 1:\n", "                ordered.append(_now_ts())  # 자동 타임스탬프\n", "            else:\n", "                ordered.append(\"\" if col not in df.columns or pd.isna(r.get(col, \"\")) else str(r[col]))\n", "        rows.append(ordered)\n", "    return rows\n", "\n", "def _append_rows(ws, rows: list[list[str]], batch_size: int = 500):\n", "    if not rows:\n", "        return\n", "    for start in range(0, len(rows), batch_size):\n", "        batch = rows[start:start+batch_size]\n", "        attempt, delay = 0, 1.0\n", "        while True:\n", "            try:\n", "                ws.append_rows(\n", "                    batch,\n", "                    value_input_option=\"RAW\",\n", "                    insert_data_option=\"INSERT_ROWS\",\n", "                    table_range=\"A1\"\n", "                )\n", "                break\n", "            except gspread.exceptions.APIError:\n", "                attempt += 1\n", "                if attempt > 5:\n", "                    raise\n", "                time.sleep(delay)\n", "                delay = min(delay * 2, 16)\n", "\n", "def upload_logs_to_sheets(df_prog: pd.DataFrame, df_gen: pd.DataFrame):\n", "    \"\"\"\n", "    df_gen : [사건번호, 전체문자열]\n", "    df_prog: [사건번호, 일자, 내용, 결과]\n", "    (둘 다 '크롤링일시'는 템플릿 마지막 열 자동 입력)\n", "    \"\"\"\n", "    client = get_client()\n", "    ss = _open_ss_by_id(client, SPREADSHEET_ID)\n", "\n", "    # 일반내용\n", "    ws_gen = _open_or_create_ws(ss, WS_GEN)\n", "    _ensure_header(ws_gen, TEMPLATE_GEN)\n", "    rows_gen = _df_to_rows_with_ts(df_gen, TEMPLATE_GEN)\n", "    _append_rows(ws_gen, rows_gen)\n", "\n", "    # 진행내용\n", "    ws_prog = _open_or_create_ws(ss, WS_PROG)\n", "    _ensure_header(ws_prog, TEMPLATE_PROG)\n", "    rows_prog = _df_to_rows_with_ts(df_prog, TEMPLATE_PROG)\n", "    _append_rows(ws_prog, rows_prog)\n", "\n", "    print(\"[✅] 구글 스프레드시트 업로드 완료 →\", WS_GEN, \"/\", WS_PROG)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5bdca190", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 환경 상수\n", "# ─────────────────────────────────────────────────────\n", "url_login   = \"https://ecfs.scourt.go.kr/psp/index.on?m=PSP101M01\"\n", "tab_btn_id  = \"mf_pfwork_tabctrl_tab_tabs2_tabHTML\"      # '아이디 로그인' 탭\n", "id_box_id   = \"mf_pfwork_ibx_elpUserId\"\n", "pwd_box_id  = \"mf_pfwork_ibx_elpUserPwd\"\n", "login_btnid = \"mf_pfwork_btn_login\"\n", "keypad_div_id = f\"nppfs-keypad-{pwd_box_id}\"\n", "\n", "user_id     = \"lawyer87\"\n", "password    = \"kkcho0904!\"\n", "\n", "CASE_ROWS_XPATH = \"//table[contains(@class,'grid')]/tbody/tr\"\n", "CASE_ANCHOR_REL = \".//a[contains(@class,'link') or self::a]\"\n", "\n", "PROG_TAB_ID   = \"mf_wfSsgoDetail_ssgoCsDetailTab_tab_ssgoTab2_tabHTML\"\n", "PROG_TABLE_ID = \"mf_wfSsgoDetail_ssgoCsDetailTab_contents_ssgoTab2_body_grd_csProgLst_body_grd_csProgLst_body_table\"\n", "\n", "# 사건번호 패턴\n", "CASE_NO_RE = re.compile(r'(?:19|20)\\d{2}\\s*[가-힣]{1,4}\\s*\\d{1,7}')\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 셀레니움 기본 설정\n", "# ─────────────────────────────────────────────────────\n", "chrome_opts = Options()\n", "chrome_opts.add_argument(\"--start-maximized\")\n", "chrome_opts.add_experimental_option(\"detach\", True)\n", "driver = webdriver.Chrome(options=chrome_opts)\n", "wait   = WebDriverWait(driver, 10)\n", "action = <PERSON><PERSON><PERSON><PERSON>(driver)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "86baca56", "metadata": {}, "outputs": [], "source": ["\n", "# ─────────────────────────────────────────────────────\n", "# 가상 키패드 입력\n", "# ─────────────────────────────────────────────────────\n", "def _enter_pwd(pwd):\n", "    try:\n", "        iframe = driver.find_element(By.XPATH, f'//iframe[contains(@id,\"{keypad_div_id}\")]')\n", "        driver.switch_to.frame(iframe)\n", "    except:\n", "        pass\n", "    wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR,'div.kpd-group.lower')))\n", "    for ch in pwd:\n", "        label = ch if ch.isdigit() else (f\"소문자 {ch}\" if ch.isalpha() else \"느낌표\")\n", "        if ch == '!':\n", "            try:\n", "                wait.until(EC.element_to_be_clickable((By.XPATH,'//img[@aria-label=\"느낌표\"]')))\n", "            except:\n", "                toggle = driver.find_element(By.XPATH,'//div[contains(@class,\"kpd-group lower\")]//img[@aria-label=\"특수문자\"]')\n", "                driver.execute_script(\"arguments[0].click();\", toggle)\n", "                wait.until(EC.element_to_be_clickable((By.XPATH,'//img[@aria-label=\"느낌표\"]')))\n", "        btn = wait.until(EC.element_to_be_clickable((By.XPATH,f'//img[@aria-label=\"{label}\"]')))\n", "        driver.execute_script(\"arguments[0].click();\", btn)\n", "        time.sleep(0.03)\n", "    for xp in (f'//*[@id=\"{keypad_div_id}\"]/div/div[5]/img[39]', '//img[@data-action=\"action:enter\"]'):\n", "        try:\n", "            btn = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.XPATH, xp)))\n", "            driver.execute_script(\"arguments[0].click();\", btn)\n", "            break\n", "        except:\n", "            continue\n", "    driver.switch_to.default_content()\n"]}, {"cell_type": "code", "execution_count": 11, "id": "e58afedd", "metadata": {}, "outputs": [], "source": ["\n", "# ─────────────────────────────────────────────────────\n", "# 유틸/네비게이션\n", "# ─────────────────────────────────────────────────────\n", "def login():\n", "    driver.get(url_login)\n", "    wait.until(EC.element_to_be_clickable((By.ID, tab_btn_id))).click()\n", "    time.sleep(0.8)\n", "    wait.until(EC.element_to_be_clickable((By.ID, id_box_id))).send_keys(user_id)\n", "    driver.find_element(By.ID, pwd_box_id).click()\n", "    wait.until(EC.visibility_of_element_located((By.ID, keypad_div_id)))\n", "    _enter_pwd(password)\n", "    wait.until(EC.element_to_be_clickable((By.ID, login_btnid))).click()\n", "    print(\"[✅] 로그인 완료\")\n", "\n", "def go_to_my_ecfs():\n", "    btn = wait.until(EC.element_to_be_clickable((By.ID, \"mf_pmf_content1_wq_uuid_286\")))\n", "    try: btn.click()\n", "    except: driver.execute_script(\"arguments[0].click();\", btn)\n", "    wait.until(EC.any_of(\n", "        EC.presence_of_element_located((By.ID, \"mf_pfmenu_v3_li_a_150202\")),\n", "        EC.presence_of_element_located((By.XPATH, \"//h2[contains(.,'나의 전자소송')]\"))\n", "    ))\n", "    print(\"[✅] 나의 전자소송 클릭 완료\")\n", "\n", "def go_to_interest_case():\n", "    time.sleep(0.5)\n", "    driver.find_element(By.ID, \"mf_pfmenu_v3_li_a_150202\").click()\n", "    driver.find_element(By.ID, \"mf_pfwork_sbx_cortList\").click()\n", "    driver.find_element(By.XPATH, '//*[@id=\"mf_pfwork_sbx_cortList\"]/option[2]').click()\n", "    time.sleep(1.5)\n", "    driver.find_element(By.ID, \"mf_pfwork_btn_search\").click()\n", "    print(\"[✅] 관심사건 조회 완료\")\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 사건번호 파싱/정제\n", "# ─────────────────────────────────────────────────────\n", "def normalize_case_no(s: str) -> str:\n", "    return re.sub(r\"\\s+\", \"\", s or \"\").strip()\n", "\n", "def pick_case_no_from_text(text: str) -> str:\n", "    if not text:\n", "        return \"\"\n", "    m = CASE_NO_RE.search(text)\n", "    if not m:\n", "        return \"\"\n", "    return normalize_case_no(m.group(0))\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 목록 → 상세 (사건번호 함께 반환)\n", "# ─────────────────────────────────────────────────────\n", "def click_case_by_index(i: int) -> str:\n", "    rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, CASE_ROWS_XPATH)))\n", "    if i < 0 or i >= len(rows):\n", "        raise IndexError(\"사건 인덱스 범위를 벗어났습니다.\")\n", "    anchor = rows[i].find_element(By.XPATH, CASE_ANCHOR_REL)\n", "    case_no_from_list = normalize_case_no(anchor.text)\n", "    driver.execute_script(\"arguments[0].click();\", anchor)\n", "    time.sleep(0.6)\n", "    if len(driver.window_handles) > 1:\n", "        driver.switch_to.window(driver.window_handles[-1])  # 헬퍼 없이 최신 창으로 전환\n", "\n", "    def has_anchor_here():\n", "        try:\n", "            driver.find_element(By.XPATH, \"//*[contains(normalize-space(.),'사건번호')]\")\n", "            return True\n", "        except:\n", "            return False\n", "\n", "    if not has_anchor_here():\n", "        driver.switch_to.default_content()\n", "        frames = driver.find_elements(By.TAG_NAME, \"iframe\")\n", "        for fr in frames:\n", "            try:\n", "                driver.switch_to.default_content()\n", "                driver.switch_to.frame(fr)\n", "                if has_anchor_here():\n", "                    return case_no_from_list\n", "            except:\n", "                continue\n", "        driver.switch_to.default_content()\n", "\n", "    return case_no_from_list\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 일반내용 추출(컨테이너 → 화면 전체 → 키입력 백업)\n", "# ─────────────────────────────────────────────────────\n", "def extract_general_text() -> str:\n", "    def _try_here():\n", "        try:\n", "            anchor = wait.until(EC.presence_of_element_located(\n", "                (By.XPATH, \"//*[contains(normalize-space(.),'사건번호')]\")\n", "            ))\n", "        except Exception:\n", "            return \"\"\n", "        try:\n", "            txt = driver.execute_script(\"\"\"\n", "                const anchor = arguments[0];\n", "                const ALLOWED = new Set(['DIV','SECTION','ARTICLE','MAIN','TABLE','TBODY','TR','TD','FORM']);\n", "                let el = anchor;\n", "                while (el && !ALLOWED.has(el.tagName)) el = el.parentElement;\n", "                const cont = el || anchor;\n", "                return (cont.innerText || '').trim();\n", "            \"\"\", anchor) or \"\"\n", "            if len(txt) >= 10:\n", "                return txt\n", "        except Exception:\n", "            pass\n", "        try:\n", "            body = (driver.execute_script(\"return (document.body.innerText || '').trim();\") or \"\")\n", "            if len(body) >= 10:\n", "                return body\n", "        except Exception:\n", "            pass\n", "        return \"\"\n", "\n", "    t = _try_here()\n", "    if len(t) >= 10:\n", "        return t\n", "\n", "    try:\n", "        driver.switch_to.default_content()\n", "        frames = driver.find_elements(By.TAG_NAME, \"iframe\")\n", "        for fr in frames:\n", "            try:\n", "                driver.switch_to.default_content()\n", "                driver.switch_to.frame(fr)\n", "                t = _try_here()\n", "                if len(t) >= 10:\n", "                    return t\n", "            except Exception:\n", "                continue\n", "    finally:\n", "        try: driver.switch_to.default_content()\n", "        except: pass\n", "\n", "    try:\n", "        body_el = wait.until(EC.presence_of_element_located((By.TAG_NAME, \"body\")))\n", "        body_el.click()\n", "        time.sleep(0.2)\n", "        action.key_down(Keys.CONTROL).send_keys(\"a\").key_up(Keys.CONTROL).perform()\n", "        time.sleep(0.2)\n", "        action.key_down(Keys.CONTROL).send_keys(\"c\").key_up(Keys.CONTROL).perform()\n", "        time.sleep(0.2)\n", "        from_text = (pyperclip.paste() or \"\").strip()\n", "        if len(from_text) >= 10:\n", "            return from_text\n", "    except Exception:\n", "        pass\n", "\n", "    return \"\"\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 진행내용 파싱\n", "# ─────────────────────────────────────────────────────\n", "def click_progress_tab_and_extract_table(case_no: str) -> list[dict]:\n", "    out_rows = []\n", "    try:\n", "        tab = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, PROG_TAB_ID)))\n", "        try:\n", "            tab.click()\n", "        except:\n", "            driver.execute_script(\"arguments[0].click();\", tab)\n", "        time.sleep(1.0)\n", "\n", "        try:\n", "            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, PROG_TABLE_ID)))\n", "        except:\n", "            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located(\n", "                (By.XPATH, \"//table[contains(@id,'grd_csProgLst') and contains(@id,'table')]\")\n", "            ))\n", "\n", "        trs = table.find_elements(By.XPATH, \".//tr\")\n", "        for tr in trs:\n", "            tds = tr.find_elements(By.XPATH, \".//td\")\n", "            if len(tds) < 3:\n", "                continue\n", "            date_txt = tds[0].text.strip()\n", "            cont_txt = tds[1].text.strip()\n", "            res_txt  = tds[2].text.strip()\n", "            if not date_txt:\n", "                continue\n", "            out_rows.append({\n", "                \"사건번호\": case_no,\n", "                \"일자\": date_txt,\n", "                \"내용\": cont_txt,\n", "                \"결과\": res_txt\n", "            })\n", "    except Exception as e:\n", "        print(f\"[WARN] 진행내용 파싱 중 예외: {e}\")\n", "    return out_rows\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 메인 크롤링\n", "# ─────────────────────────────────────────────────────\n", "def get_case_count() -> int:\n", "    rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, CASE_ROWS_XPATH)))\n", "    return len(rows)\n", "\n", "def crawl_all_cases_by_index():\n", "    all_general_rows  = []\n", "    all_progress_rows = []\n", "\n", "    total = 1 # get_case_count()\n", "    print(f\"[INFO] 사건 건수: {total}건\")\n", "\n", "    for i in range(total):\n", "        try:\n", "            print(f\"\\n[DEBUG] {i+1}/{total}번째 사건 상세 진입 시도\")\n", "            case_no_from_list = click_case_by_index(i)\n", "\n", "            full_text = extract_general_text()\n", "            if not full_text:\n", "                print(\"[WARN] 일반내용 텍스트 추출 실패 → 본 사건 스킵\")\n", "                if len(driver.window_handles) > 1:\n", "                    driver.close(); driver.switch_to.window(driver.window_handles[0])\n", "                time.sleep(0.5)\n", "                continue\n", "\n", "            case_no = pick_case_no_from_text(case_no_from_list) or pick_case_no_from_text(full_text) or case_no_from_list\n", "            case_no = normalize_case_no(case_no)\n", "\n", "            # 일반내용 행 (타임스탬프는 업로드에서 자동 주입)\n", "            row_general = {\n", "                \"사건번호\": case_no,\n", "                \"전체문자열\": full_text,\n", "            }\n", "            all_general_rows.append(row_general)\n", "\n", "            # 진행내용 행들 (타임스탬프는 업로드에서 자동 주입)\n", "            prog_rows = click_progress_tab_and_extract_table(case_no)\n", "            all_progress_rows.extend(prog_rows)\n", "\n", "        except Exception as e:\n", "            print(f\"[WARN] 사건({i}) 처리 중 예외: {e}\")\n", "        finally:\n", "            if len(driver.window_handles) > 1:\n", "                driver.close(); driver.switch_to.window(driver.window_handles[0])\n", "            time.sleep(0.5)\n", "\n", "    # ── Data<PERSON><PERSON>e 및 컬럼 순서 고정 ─────────────────\n", "    df_general_out  = pd.DataFrame(all_general_rows)\n", "    df_progress_out = pd.DataFrame(all_progress_rows)\n", "\n", "    if not df_general_out.empty:\n", "        desired_cols_gen = [\"사건번호\", \"전체문자열\"]  # '크롤링일시'는 템플릿 마지막 열 자동 주입\n", "        df_general_out = df_general_out.reindex(columns=desired_cols_gen)\n", "\n", "    if not df_progress_out.empty and \"일자\" in df_progress_out.columns:\n", "        try:\n", "            s = pd.to_datetime(df_progress_out[\"일자\"], errors=\"coerce\")\n", "            df_progress_out.loc[s.notna(), \"일자\"] = s[s.notna()].dt.strftime(\"%Y-%m-%d\")\n", "            df_progress_out = df_progress_out.sort_values([\"사건번호\", \"일자\"], kind=\"stable\")\n", "        except Exception:\n", "            pass\n", "    from pprint import pprint\n", "    return df_general_out, df_progress_out\n", "    pprint(df_general_out)\n", "    pprint(df_progress_out)\n", "    # # ── 구글 스프레드시트 업로드 ──\n", "    # if df_general_out.empty and df_progress_out.empty:\n", "    #     print(\"\\n[⚠️] 업로드할 데이터가 없습니다.\")\n", "    # else:\n", "    #     upload_logs_to_sheets(df_prog=df_progress_out, df_gen=df_general_out)\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "bd2fcf84", "metadata": {}, "outputs": [{"ename": "ElementClickInterceptedException", "evalue": "Message: element click intercepted: Element <input type=\"button\" tabindex=\"0\" id=\"mf_pfwork_btn_login\" class=\"w2trigger btn_cm lg confirm\" value=\"로그인\" title=\"로그인\" name=\"mf_pfwork_btn_login\"> is not clickable at point (748, 600). Other element would receive the click: <div id=\"_modal\" class=\"w2modal_popup\" style=\"display: block;\"></div>\n  (Session info: chrome=139.0.7258.139); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception\nStacktrace:\n0   chromedriver                        0x000000010482ae00 cxxbridge1$str$ptr + 2742224\n1   chromedriver                        0x0000000104822d00 cxxbridge1$str$ptr + 2709200\n2   chromedriver                        0x000000010436d0b8 cxxbridge1$string$len + 90520\n3   chromedriver                        0x00000001043ba168 cxxbridge1$string$len + 406088\n4   chromedriver                        0x00000001043b86d4 cxxbridge1$string$len + 399284\n5   chromedriver                        0x00000001043b64e8 cxxbridge1$string$len + 390600\n6   chromedriver                        0x00000001043b58e4 cxxbridge1$string$len + 387524\n7   chromedriver                        0x00000001043aa418 cxxbridge1$string$len + 341240\n8   chromedriver                        0x00000001043a9ea4 cxxbridge1$string$len + 339844\n9   chromedriver                        0x00000001043f5980 cxxbridge1$string$len + 649824\n10  chromedriver                        0x00000001043a88f4 cxxbridge1$string$len + 334292\n11  chromedriver                        0x00000001047ee478 cxxbridge1$str$ptr + 2494024\n12  chromedriver                        0x00000001047f16a4 cxxbridge1$str$ptr + 2506868\n13  chromedriver                        0x00000001047cf3b0 cxxbridge1$str$ptr + 2366848\n14  chromedriver                        0x00000001047f1f4c cxxbridge1$str$ptr + 2509084\n15  chromedriver                        0x00000001047c04a8 cxxbridge1$str$ptr + 2305656\n16  chromedriver                        0x0000000104811644 cxxbridge1$str$ptr + 2637844\n17  chromedriver                        0x00000001048117d0 cxxbridge1$str$ptr + 2638240\n18  chromedriver                        0x000000010482294c cxxbridge1$str$ptr + 2708252\n19  libsystem_pthread.dylib             0x000000018e687c0c _pthread_start + 136\n20  libsystem_pthread.dylib             0x000000018e682b80 thread_start + 8\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mElementClickInterceptedException\u001b[39m          Traceback (most recent call last)", "\u001b[36m<PERSON>ell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mlogin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 12\u001b[39m, in \u001b[36mlogin\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     10\u001b[39m wait.until(EC.visibility_of_element_located((By.ID, keypad_div_id)))\n\u001b[32m     11\u001b[39m _enter_pwd(password)\n\u001b[32m---> \u001b[39m\u001b[32m12\u001b[39m \u001b[43mwait\u001b[49m\u001b[43m.\u001b[49m\u001b[43muntil\u001b[49m\u001b[43m(\u001b[49m\u001b[43mEC\u001b[49m\u001b[43m.\u001b[49m\u001b[43melement_to_be_clickable\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mBy\u001b[49m\u001b[43m.\u001b[49m\u001b[43mID\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogin_btnid\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mclick\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m[✅] 로그인 완료\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Repo/layer_crawling/.venv/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:120\u001b[39m, in \u001b[36mWebElement.click\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    112\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mclick\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    113\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Clicks the element.\u001b[39;00m\n\u001b[32m    114\u001b[39m \n\u001b[32m    115\u001b[39m \u001b[33;03m    Example:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    118\u001b[39m \u001b[33;03m    >>> element.click()\u001b[39;00m\n\u001b[32m    119\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m120\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_execute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCLICK_ELEMENT\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Repo/layer_crawling/.venv/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:573\u001b[39m, in \u001b[36mWebElement._execute\u001b[39m\u001b[34m(self, command, params)\u001b[39m\n\u001b[32m    571\u001b[39m     params = {}\n\u001b[32m    572\u001b[39m params[\u001b[33m\"\u001b[39m\u001b[33mid\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28mself\u001b[39m._id\n\u001b[32m--> \u001b[39m\u001b[32m573\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_parent\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Repo/layer_crawling/.venv/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:458\u001b[39m, in \u001b[36mWebDriver.execute\u001b[39m\u001b[34m(self, driver_command, params)\u001b[39m\n\u001b[32m    455\u001b[39m response = cast(RemoteConnection, \u001b[38;5;28mself\u001b[39m.command_executor).execute(driver_command, params)\n\u001b[32m    457\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[32m--> \u001b[39m\u001b[32m458\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43merror_handler\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheck_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    459\u001b[39m     response[\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28mself\u001b[39m._unwrap_value(response.get(\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[32m    460\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Repo/layer_crawling/.venv/lib/python3.12/site-packages/selenium/webdriver/remote/errorhandler.py:232\u001b[39m, in \u001b[36mErrorHandler.check_response\u001b[39m\u001b[34m(self, response)\u001b[39m\n\u001b[32m    230\u001b[39m         alert_text = value[\u001b[33m\"\u001b[39m\u001b[33malert\u001b[39m\u001b[33m\"\u001b[39m].get(\u001b[33m\"\u001b[39m\u001b[33mtext\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    231\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace, alert_text)  \u001b[38;5;66;03m# type: ignore[call-arg]  # mypy is not smart enough here\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m232\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace)\n", "\u001b[31mElementClickInterceptedException\u001b[39m: Message: element click intercepted: Element <input type=\"button\" tabindex=\"0\" id=\"mf_pfwork_btn_login\" class=\"w2trigger btn_cm lg confirm\" value=\"로그인\" title=\"로그인\" name=\"mf_pfwork_btn_login\"> is not clickable at point (748, 600). Other element would receive the click: <div id=\"_modal\" class=\"w2modal_popup\" style=\"display: block;\"></div>\n  (Session info: chrome=139.0.7258.139); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception\nStacktrace:\n0   chromedriver                        0x000000010482ae00 cxxbridge1$str$ptr + 2742224\n1   chromedriver                        0x0000000104822d00 cxxbridge1$str$ptr + 2709200\n2   chromedriver                        0x000000010436d0b8 cxxbridge1$string$len + 90520\n3   chromedriver                        0x00000001043ba168 cxxbridge1$string$len + 406088\n4   chromedriver                        0x00000001043b86d4 cxxbridge1$string$len + 399284\n5   chromedriver                        0x00000001043b64e8 cxxbridge1$string$len + 390600\n6   chromedriver                        0x00000001043b58e4 cxxbridge1$string$len + 387524\n7   chromedriver                        0x00000001043aa418 cxxbridge1$string$len + 341240\n8   chromedriver                        0x00000001043a9ea4 cxxbridge1$string$len + 339844\n9   chromedriver                        0x00000001043f5980 cxxbridge1$string$len + 649824\n10  chromedriver                        0x00000001043a88f4 cxxbridge1$string$len + 334292\n11  chromedriver                        0x00000001047ee478 cxxbridge1$str$ptr + 2494024\n12  chromedriver                        0x00000001047f16a4 cxxbridge1$str$ptr + 2506868\n13  chromedriver                        0x00000001047cf3b0 cxxbridge1$str$ptr + 2366848\n14  chromedriver                        0x00000001047f1f4c cxxbridge1$str$ptr + 2509084\n15  chromedriver                        0x00000001047c04a8 cxxbridge1$str$ptr + 2305656\n16  chromedriver                        0x0000000104811644 cxxbridge1$str$ptr + 2637844\n17  chromedriver                        0x00000001048117d0 cxxbridge1$str$ptr + 2638240\n18  chromedriver                        0x000000010482294c cxxbridge1$str$ptr + 2708252\n19  libsystem_pthread.dylib             0x000000018e687c0c _pthread_start + 136\n20  libsystem_pthread.dylib             0x000000018e682b80 thread_start + 8\n"]}], "source": ["login()\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4b19d8b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[✅] 나의 전자소송 클릭 완료\n"]}], "source": ["\n", "go_to_my_ecfs()"]}, {"cell_type": "code", "execution_count": null, "id": "d2e73ade", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[✅] 관심사건 조회 완료\n", "[INFO] 사건 건수: 9건\n", "\n", "[DEBUG] 1/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 2/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 3/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 4/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 5/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 6/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 7/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 8/9번째 사건 상세 진입 시도\n", "\n", "[DEBUG] 9/9번째 사건 상세 진입 시도\n", "         사건번호            전체문자열\n", "0   2025고단640  df_progress_out\n", "1  2025고단1836  df_progress_out\n", "2  2024고단3260  df_progress_out\n", "3   2025노1059  df_progress_out\n", "4    2025노938  df_progress_out\n", "5   2024노1423  df_progress_out\n", "6   2024노3625  df_progress_out\n", "7  2024고정1086  df_progress_out\n", "8    2025노566  df_progress_out\n", "           사건번호          일자  \\\n", "175  2024고단3260  2024-09-12   \n", "176  2024고단3260  2024-10-10   \n", "177  2024고단3260  2024-10-10   \n", "178  2024고단3260  2024-10-10   \n", "179  2024고단3260  2024-10-23   \n", "..          ...         ...   \n", "348    2025노938  2025-06-10   \n", "349    2025노938  2025-06-10   \n", "350    2025노938  2025-06-10   \n", "351    2025노938  2025-06-11   \n", "352    2025노938  2025-06-17   \n", "\n", "                                                    내용             결과  \n", "175                                              공소장접수                 \n", "176                              (피고인 신OO) 피고인 분리·병합결정                 \n", "177                 피고인1 신OO 공소장부본/국선변호인선정을위한고지/의견서 발송  위의 '확인' 항목 체크  \n", "178  피고인1 신OO 분리.병합결정(24고단3476)/공소장/국선변호인선정을위한고지/의견...  위의 '확인' 항목 체크  \n", "179                                 피고인1 신OO 피고인소환장 발송  위의 '확인' 항목 체크  \n", "..                                                 ...            ...  \n", "348                                       - 피고인 장OO 출석           판결선고  \n", "349                                      - 변호인 조경국 불출석           판결선고  \n", "350                            피고인 장OO 종국 : 항소기각판결(변론)                 \n", "351                             (피고인 장OO) 피고인 구속기간갱신결정                 \n", "352                                  피고인1 변호인 조OO 판결등본  2025.06.17 발급  \n", "\n", "[469 rows x 4 columns]\n"]}], "source": ["go_to_interest_case()"]}, {"cell_type": "code", "execution_count": null, "id": "fb611b82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] 사건 건수: 1건\n", "\n", "[DEBUG] 1/1번째 사건 상세 진입 시도\n"]}], "source": ["\n", "df_general_out, df_progress_out = crawl_all_cases_by_index()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "772abaa0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         사건번호          일자                                                 내용  \\\n", "0   2025고단640  2025-02-21                                              공소장접수   \n", "1   2025고단640  2025-02-26  피고인1 이OO 공소장부본/의견서/국민참여재판의사확인서/안내서/국선변호인선정을위한고...   \n", "2   2025고단640  2025-03-04                        피고인 이OO 의견서(답변서/정상관계진술서) 제출   \n", "3   2025고단640  2025-03-04                             피고인 이OO 국선변호인청구(희망) 제출   \n", "4   2025고단640  2025-05-07                            (피고인 이OO) 피고인 국선변호인선정결정   \n", "5   2025고단640  2025-05-08                    피고인1 변호인 조경국 국선변호인선정결정/공소장부본 발송   \n", "6   2025고단640  2025-05-08                              피고인1 이OO 국선변호인선정결정 발송   \n", "7   2025고단640  2025-05-23                      검사 대전지방검찰청검사장 공판기일통지서(검찰용) 발송   \n", "8   2025고단640  2025-05-26                                 피고인1 이OO 피고인소환장 발송   \n", "9   2025고단640  2025-05-26                      피고인1 변호인 조경국 공판기일통지서(변호인용) 발송   \n", "10  2025고단640  2025-07-10                            피고인1 변호인 조OO 변호인 의견서 제출   \n", "11  2025고단640  2025-07-11                            공판기일(본관 제231호 법정 10:40)   \n", "12  2025고단640  2025-07-11                                   - 검사 정수진, 김홍래 출석   \n", "13  2025고단640  2025-07-11                                       - 피고인 이OO 출석   \n", "14  2025고단640  2025-07-11                                       - 변호인 조경국 출석   \n", "15  2025고단640  2025-07-15                                     검사 양희재 증인신청 제출   \n", "16  2025고단640  2025-07-15                                     검사 양희재 증인신청 제출   \n", "17  2025고단640  2025-07-16                                    증인 정OO 증인소환장 발송   \n", "18  2025고단640  2025-07-22                           피고인1 변호인 조OO 사실조회촉탁신청 제출   \n", "19  2025고단640  2025-07-22                           피고인1 변호인 조OO 사실조회촉탁신청 제출   \n", "20  2025고단640  2025-07-28                               피고인1 변호인 조OO 증인신청 제출   \n", "21  2025고단640  2025-07-29                                    증인 김OO 증인소환장 발송   \n", "22  2025고단640  2025-08-21                        피고인1 변호인 조OO 증인신문 기일변경신청 제출   \n", "23  2025고단640  2025-08-29                            공판기일(본관 제231호 법정 16:10)   \n", "24  2025고단640  2025-08-29                                           - 검사 양희지   \n", "25  2025고단640  2025-08-29                                          - 피고인 이OO   \n", "26  2025고단640  2025-08-29                                          - 변호인 조경국   \n", "27  2025고단640  2025-08-29                                           - 증인 김OO   \n", "28  2025고단640  2025-08-29                                           - 증인 이OO   \n", "29  2025고단640  2025-08-29                                           - 증인 정OO   \n", "30  2025고단640  2025-10-17                            공판기일(본관 제231호 법정 14:00)   \n", "31  2025고단640  2025-10-17                                           - 검사 양희지   \n", "32  2025고단640  2025-10-17                                          - 피고인 이OO   \n", "33  2025고단640  2025-10-17                                          - 변호인 조경국   \n", "34  2025고단640  2025-10-17                                           - 증인 이OO   \n", "35  2025고단640  2025-10-17                                           - 증인 김OO   \n", "36  2025고단640  2025-10-17                                           - 증인 정OO   \n", "\n", "               결과  \n", "0                  \n", "1   위의 '확인' 항목 체크  \n", "2                  \n", "3                  \n", "4                  \n", "5   위의 '확인' 항목 체크  \n", "6   위의 '확인' 항목 체크  \n", "7   위의 '확인' 항목 체크  \n", "8   위의 '확인' 항목 체크  \n", "9   위의 '확인' 항목 체크  \n", "10                 \n", "11                 \n", "12             속행  \n", "13             속행  \n", "14             속행  \n", "15                 \n", "16                 \n", "17  위의 '확인' 항목 체크  \n", "18                 \n", "19                 \n", "20                 \n", "21  위의 '확인' 항목 체크  \n", "22                 \n", "23                 \n", "24           기일변경  \n", "25           기일변경  \n", "26           기일변경  \n", "27           기일변경  \n", "28           기일변경  \n", "29           기일변경  \n", "30                 \n", "31                 \n", "32                 \n", "33                 \n", "34                 \n", "35                 \n", "36                 \n"]}], "source": ["from pprint import pprint\n", "\n", "pprint(df_progress_out)"]}, {"cell_type": "code", "execution_count": null, "id": "8aea91ec", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# ─────────────────────────────────────────────────────\n", "# 실행\n", "# ─────────────────────────────────────────────────────\n", "if __name__ == \"__main__\":\n", "    try:\n", "        login()\n", "        go_to_my_ecfs()\n", "        go_to_interest_case()\n", "        crawl_all_cases_by_index()\n", "    finally:\n", "        print(\"[DONE]\")\n", "        try:\n", "            driver.quit()\n", "        except:\n", "            pass\n"]}], "metadata": {"kernelspec": {"display_name": "layer-crawling", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}