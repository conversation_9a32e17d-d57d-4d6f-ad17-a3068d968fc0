"""Electronic Litigation crawler implementation."""

import time
import re
from typing import Dict, Any, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from .base import BaseCrawler
from ..models.case_data import ElectronicLitigationCase, ProgressEntry, CrawlResult
from ..models.config import ElectronicLitigationConfig, ElectronicLitigationCredentials
from ..services.webdriver_manager import WebDriverManager
from ..utils.logger import get_logger
from ..utils.exceptions import CrawlingError, AuthenticationError, WebDriverError
from ..utils.decorators import handle_cert_installation_popup


class ElectronicLitigationCrawler(BaseCrawler):
    """Crawler for Electronic Litigation system."""
    
    def __init__(self, config: ElectronicLitigationConfig, webdriver_manager: WebDriverManager):
        super().__init__(config.__dict__)
        self.config = config
        self.webdriver_manager = webdriver_manager
        self.logger = get_logger(self.__class__.__name__)
        self._current_credentials: ElectronicLitigationCredentials = None
        
        # Constants
        self.CASE_ROWS_XPATH = "//table[contains(@class,'grid')]/tbody/tr"
        self.CASE_ANCHOR_REL = ".//a[contains(@class,'link') or self::a]"
        self.PROG_TAB_ID = "mf_wfSsgoDetail_ssgoCsDetailTab_tab_ssgoTab2_tabHTML"
        # self.PROG_TABLE_ID = "mf_wfSsgoDetail_ssgoCsDetailTab_contents_ssgoTab2_body_grd_csProgLst_body_grd_csProgLst_body_table"
        # TODO: 확인 필요. 위 코드를 사용하는것이 잘 되고 있었음.
        self.PROG_TABLE_ID = "mf_wfSsgoDetail_ssgoCsDetailTab_contents_ssgoTab2_body_grd_csProgLst_body_table"
        self.CASE_NO_PATTERN = re.compile(r'(?:19|20)\d{2}\s*[가-힣]{1,4}\s*\d{1,7}')
    
    def authenticate(self, credentials: ElectronicLitigationCredentials) -> bool:
        """
        Authenticate with the electronic litigation system.
        
        Args:
            credentials: Electronic litigation credentials
            
        Returns:
            bool: True if authentication successful
            
        Raises:
            AuthenticationError: If authentication fails
        """
        try:
            self._current_credentials = credentials
            
            # Navigate to login page
            self.webdriver_manager.navigate_to(self.config.base_url)
            
            # Click on ID login tab
            tab_button = self.webdriver_manager.wait_for_clickable(
                By.ID, "mf_pfwork_tabctrl_tab_tabs2_tabHTML"
            )
            self.webdriver_manager.safe_click(tab_button)
            time.sleep(0.8)
            
            # Enter username
            username_field = self.webdriver_manager.wait_for_clickable(By.ID, "mf_pfwork_ibx_elpUserId")
            username_field.send_keys(credentials.username)
            
            # Click on password field to activate virtual keypad
            password_field = self.webdriver_manager.driver.find_element(By.ID, "mf_pfwork_ibx_elpUserPwd")
            password_field.click()
            
            # Wait for virtual keypad to appear
            keypad_div_id = f"nppfs-keypad-mf_pfwork_ibx_elpUserPwd"
            self.webdriver_manager.wait_for_element(By.ID, keypad_div_id)
            
            # Enter password using virtual keypad
            self._enter_password_via_keypad(credentials.password, keypad_div_id)
            
            self._click_login_button()
            
            # Verify login success
            try:
                self.webdriver_manager.wait_for_element(By.ID, "mf_pmf_content1_wq_uuid_286", timeout=180)
                self.logger.info(f"Successfully authenticated as {credentials.username}")
                return True
            except TimeoutException:
                raise AuthenticationError(f"Login verification failed for {credentials.username}")
                
        except Exception as e:
            self.logger.error(f"Authentication failed for {credentials.username}: {e}")
            raise AuthenticationError(f"Authentication failed: {e}") from e
    
    def crawl_cases(self, **kwargs) -> CrawlResult:
        """
        Crawl electronic litigation cases.
        
        Args:
            **kwargs: Additional parameters
            
        Returns:
            CrawlResult: Crawling results
        """
        try:
            if not self._current_credentials:
                raise CrawlingError("Not authenticated. Call authenticate() first.")
            
            self.logger.info("Starting electronic litigation crawl")
            
            # TODO: 관심사건 등록되지 않은 것은 관심사건으로 등록 해야 함.
            

            # Navigate to interest cases
            self._navigate_to_interest_cases()
            
            # Get case count
            case_count = self._get_case_count()
            self.logger.info(f"Found {case_count} interest cases")
            
            # Extract case details
            cases = self._extract_all_cases(case_count)
            
            self.logger.info(f"Successfully crawled {len(cases)} cases")
            
            return CrawlResult(
                success=True,
                cases=cases,
                crawl_timestamp=self._start_time
            )
            
        except Exception as e:
            self.logger.error(f"Crawling failed: {e}")
            return CrawlResult(
                success=False,
                error_message=str(e),
                crawl_timestamp=self._start_time
            )

    def cleanup(self) -> None:
        """Clean up resources."""
        if self.webdriver_manager:
            self.webdriver_manager.cleanup()

    def _handle_cert_installation_popup(self) -> bool:
        """
        Check for and handle certificate installation popup with "아니요" button if present.

        This method specifically handles popups that appear when certificate programs
        are not installed, typically asking users to install certificate software.

        Returns:
            bool: True if popup was found and handled, False otherwise
        """
        # Multiple selector patterns for certificate installation popup
        popup_selectors = [
            # Primary pattern: ID starts with "confirm" and ends with "_wframe_btn_cancel" with value "아니요"
            '//input[starts-with(@id, "confirm") and contains(@id, "_wframe_btn_cancel") and @value="아니요"]',

            # Alternative pattern: Class and value combination
            '//input[@class="w2trigger btn_cm pop" and @value="아니요"]',

            # Broader pattern: Contains "_wframe_btn_cancel" with value "아니요" and btn_cm class
            '//input[contains(@id, "_wframe_btn_cancel") and @value="아니요" and contains(@class, "btn_cm")]',

            # Even broader pattern: Any input with value "아니요" and popup-related classes
            '//input[@value="아니요" and (contains(@class, "pop") or contains(@class, "btn_cm"))]'
        ]

        for i, selector in enumerate(popup_selectors):
            try:
                popup_button = self.webdriver_manager.wait_for_clickable(
                    By.XPATH, selector, timeout=1
                )

                if popup_button.is_displayed():
                    self.webdriver_manager.safe_click(popup_button, use_javascript=True)
                    self.logger.info(f"Certificate installation popup handled with selector pattern {i+1}: {selector}")
                    time.sleep(0.5)  # Brief pause after popup handling
                    return True

            except TimeoutException:
                # This selector didn't find the popup, try next one
                continue
            except Exception as e:
                self.logger.debug(f"Error with selector pattern {i+1} ({selector}): {e}")
                continue

        # No popup found with any selector
        return False
    

    @handle_cert_installation_popup()
    def _click_login_button(self) -> None:
        # Click login button
        login_button = self.webdriver_manager.wait_for_clickable(By.ID, "mf_pfwork_btn_login")
        self.webdriver_manager.safe_click(login_button)

    @handle_cert_installation_popup()
    def _enter_password_via_keypad(self, password: str, keypad_div_id: str) -> None:
        """Enter password using virtual keypad."""
        try:
            # Try to switch to keypad iframe if it exists
            try:
                iframe = self.webdriver_manager.driver.find_element(
                    By.XPATH, f'//iframe[contains(@id,"{keypad_div_id}")]'
                )
                self.webdriver_manager.driver.switch_to.frame(iframe)
            except NoSuchElementException:
                # No iframe, keypad is in main document
                pass
            
            # Wait for keypad to be visible
            self.webdriver_manager.wait_for_element(By.CSS_SELECTOR, 'div.kpd-group.lower')
            
            # Enter each character
            for char in password:
                self._click_keypad_character(char)
                time.sleep(0.08)
            
            # Click enter button
            enter_selectors = [
                f'//*[@id="{keypad_div_id}"]/div/div[5]/img[39]',
                '//img[@data-action="action:enter"]'
            ]
            
            for selector in enter_selectors:
                try:
                    enter_button = self.webdriver_manager.wait_for_clickable(By.XPATH, selector, timeout=5)
                    self.webdriver_manager.safe_click(enter_button, use_javascript=True)
                    break
                except TimeoutException:
                    continue
            
            # Switch back to main content
            self.webdriver_manager.driver.switch_to.default_content()
            
        except Exception as e:
            self.logger.error(f"Failed to enter password via keypad: {e}")
            raise AuthenticationError(f"Failed to enter password via keypad: {e}") from e
    
    @handle_cert_installation_popup()
    def _click_keypad_character(self, char: str) -> None:
        """Click a character on the virtual keypad."""
        try:
            if char.isdigit():
                label = char
            elif char.isalpha():
                label = f"소문자 {char}"
            elif char == '!':
                # Handle special character
                try:
                    # Check if exclamation mark is already visible
                    self.webdriver_manager.wait_for_clickable(By.XPATH, '//img[@aria-label="느낌표"]', timeout=2)
                except (TimeoutException, WebDriverError):
                    # Need to toggle to special characters
                    toggle_button = self.webdriver_manager.driver.find_element(
                        By.XPATH, '//div[contains(@class,"kpd-group lower")]//img[@aria-label="특수문자"]'
                    )
                    self.webdriver_manager.safe_click(toggle_button, use_javascript=True)
                    self.webdriver_manager.wait_for_element(By.XPATH, '//img[@aria-label="느낌표"]')
                label = "느낌표"
            else:
                label = char
            
            # Click the character button
            char_button = self.webdriver_manager.wait_for_clickable(
                By.XPATH, f'//img[@aria-label="{label}"]'
            )
            self.webdriver_manager.safe_click(char_button, use_javascript=True)
            
        except Exception as e:
            self.logger.error(f"Failed to click keypad character '{char}': {e}")
            raise
    
    @handle_cert_installation_popup()
    def _navigate_to_interest_cases(self) -> None:
        """Navigate to interest cases section."""
        try:
            # Click "나의 전자소송" button
            my_ecfs_button = self.webdriver_manager.wait_for_clickable(By.ID, "mf_pmf_content1_wq_uuid_286")
            self.webdriver_manager.safe_click(my_ecfs_button)
            
            # Wait for navigation
            self.webdriver_manager.wait_for_element(By.ID, "mf_pfmenu_v3_li_a_150202")
            self.logger.info("Navigated to 나의 전자소송")
            
            # Click interest cases menu
            time.sleep(0.5)
            interest_cases_link = self.webdriver_manager.driver.find_element(By.ID, "mf_pfmenu_v3_li_a_150202")
            interest_cases_link.click()
            
            # Select court dropdown (second option)
            court_dropdown = self.webdriver_manager.driver.find_element(By.ID, "mf_pfwork_sbx_cortList")
            court_dropdown.click()
            
            court_option = self.webdriver_manager.driver.find_element(
                By.XPATH, '//*[@id="mf_pfwork_sbx_cortList"]/option[2]'
            )
            court_option.click()
            
            time.sleep(1.5)
            
            # Click search button
            search_button = self.webdriver_manager.driver.find_element(By.ID, "mf_pfwork_btn_search")
            search_button.click()
            
            self.logger.info("Navigated to interest cases and executed search")
            
        except Exception as e:
            self.logger.error(f"Failed to navigate to interest cases: {e}")
            raise CrawlingError(f"Failed to navigate to interest cases: {e}") from e

    def _get_case_count(self) -> int:
        """Get the number of cases in the results."""
        try:
            rows = self.webdriver_manager.wait.until(
                EC.presence_of_all_elements_located((By.XPATH, self.CASE_ROWS_XPATH))
            )
            return len(rows)
        except Exception as e:
            self.logger.error(f"Failed to get case count: {e}")
            raise CrawlingError(f"Failed to get case count: {e}") from e

    def _extract_all_cases(self, case_count: int) -> List[ElectronicLitigationCase]:
        """Extract all case details."""
        cases = []

        for i in range(case_count):
            try:
                self.logger.debug(f"Processing case {i+1}/{case_count}")

                # Click on case and get case number
                case_number = self._click_case_by_index(i)

                # Extract general content
                general_content = self._extract_general_content()

                if not general_content:
                    self.logger.warning(f"No general content found for case {i+1}, skipping")
                    self._close_case_window()
                    continue

                # Extract case number from content if not found from list
                if not case_number:
                    case_number = self._extract_case_number_from_text(general_content)

                case_number = self._normalize_case_number(case_number)

                # Extract progress entries
                progress_entries = self._extract_progress_entries(case_number)

                # Create case object
                case = ElectronicLitigationCase(
                    case_number=case_number,
                    general_content=general_content,
                    progress_entries=progress_entries
                )

                cases.append(case)
                self.logger.debug(f"Extracted case: {case_number}")

                # Close case window
                self._close_case_window()

            except Exception as e:
                self.logger.error(f"Failed to process case {i+1}: {e}")
                self._close_case_window()
                continue

        return cases

    @handle_cert_installation_popup()
    def _click_case_by_index(self, index: int) -> str:
        """Click on a case by index and return case number."""
        try:
            rows = self.webdriver_manager.wait.until(
                EC.presence_of_all_elements_located((By.XPATH, self.CASE_ROWS_XPATH))
            )

            if index >= len(rows):
                raise IndexError(f"Case index {index} out of range")

            # Find the anchor element in the row
            anchor = rows[index].find_element(By.XPATH, self.CASE_ANCHOR_REL)
            case_number_from_list = self._normalize_case_number(anchor.text)

            # Click the anchor
            self.webdriver_manager.safe_click(anchor, use_javascript=True)
            time.sleep(0.6)

            # Handle potential new window
            if len(self.webdriver_manager.driver.window_handles) > 1:
                self.webdriver_manager.driver.switch_to.window(
                    self.webdriver_manager.driver.window_handles[-1]
                )

            # Check if we need to switch to iframe
            self._switch_to_case_content_frame()

            return case_number_from_list

        except Exception as e:
            self.logger.error(f"Failed to click case {index}: {e}")
            raise CrawlingError(f"Failed to click case {index}: {e}") from e

    @handle_cert_installation_popup()
    def _switch_to_case_content_frame(self) -> None:
        """Switch to the appropriate frame containing case content."""
        try:
            # Check if case number element is in current context
            try:
                self.webdriver_manager.driver.find_element(
                    By.XPATH, "//*[contains(normalize-space(.),'사건번호')]"
                )
                return  # Already in correct context
            except NoSuchElementException:
                pass

            # Try to find and switch to iframe
            self.webdriver_manager.driver.switch_to.default_content()
            frames = self.webdriver_manager.driver.find_elements(By.TAG_NAME, "iframe")

            for frame in frames:
                try:
                    self.webdriver_manager.driver.switch_to.default_content()
                    self.webdriver_manager.driver.switch_to.frame(frame)

                    # Check if case content is in this frame
                    self.webdriver_manager.driver.find_element(
                        By.XPATH, "//*[contains(normalize-space(.),'사건번호')]"
                    )
                    return  # Found correct frame

                except NoSuchElementException:
                    continue

            # If no frame found, switch back to default
            self.webdriver_manager.driver.switch_to.default_content()

        except Exception as e:
            self.logger.warning(f"Error switching to case content frame: {e}")

    def _extract_general_content(self) -> str:
        """Extract general case content."""
        try:
            # Try multiple methods to extract content

            # Method 1: Find container and extract text
            try:
                anchor = self.webdriver_manager.wait_for_element(
                    By.XPATH, "//*[contains(normalize-space(.),'사건번호')]", timeout=10
                )

                content = self.webdriver_manager.driver.execute_script("""
                    const anchor = arguments[0];
                    const ALLOWED = new Set(['DIV','SECTION','ARTICLE','MAIN','TABLE','TBODY','TR','TD','FORM']);
                    let el = anchor;
                    while (el && !ALLOWED.has(el.tagName)) el = el.parentElement;
                    const cont = el || anchor;
                    return (cont.innerText || '').trim();
                """, anchor)
                self.logger.debug(f"Extracted general content fron method 1: {content}")
                if len(content) >= 10:
                    return content

            except Exception:
                pass

            # Method 2: Get body text
            try:
                body_text = self.webdriver_manager.driver.execute_script(
                    "return (document.body.innerText || '').trim();"
                )
                self.logger.debug(f"Extracted general content fron method 2: {body_text}")
                if len(body_text) >= 10:
                    return body_text
            except Exception:
                pass

            # Method 3: Use keyboard shortcuts as fallback
            try:
                body_element = self.webdriver_manager.wait_for_element(By.TAG_NAME, "body")
                body_element.click()
                time.sleep(0.2)

                for i in range(3):
                    try:
                        clipboard_content = self._copy_all_and_get()
                        self.logger.debug(f"Extracted general content fron method 3-{i}: {clipboard_content}")
                        if len(clipboard_content.strip()) >= 10:
                            return clipboard_content.strip()
                    except CrawlingError:
                        continue

            except Exception:
                pass
            self.logger.debug(f"Failed to extract general content")
            return ""

        except Exception as e:
            self.logger.error(f"Failed to extract general content: {e}")
            return ""
        
    def _copy_all_and_get(self):
        # Select all and copy
        self.webdriver_manager.action_chains.key_down(Keys.CONTROL).send_keys("a").key_up(Keys.CONTROL).perform()
        time.sleep(0.2)
        self.webdriver_manager.action_chains.key_down(Keys.CONTROL).send_keys("c").key_up(Keys.CONTROL).perform()
        time.sleep(0.2)
        import pyperclip
        clipboard_content = pyperclip.paste() or ""
        if (clipboard_content.strip().startswith(('curl', '-H "Authorization:', 'Bearer')) or 
            'Authorization: Bearer' in clipboard_content):
            raise CrawlingError("Clipboard contains curl command, skipping")
        return clipboard_content

    def _extract_progress_entries(self, case_number: str) -> List[ProgressEntry]:
        """Extract progress entries for a case."""
        progress_entries = []

        try:
            # Click on progress tab
            progress_tab = self.webdriver_manager.wait_for_clickable(By.ID, self.PROG_TAB_ID, timeout=10)
            self.webdriver_manager.safe_click(progress_tab)
            time.sleep(1.0)

            # Find progress table
            try:
                progress_table = self.webdriver_manager.wait_for_element(By.ID, self.PROG_TABLE_ID, timeout=10)
            except TimeoutException:
                # Try alternative selector
                progress_table = self.webdriver_manager.wait_for_element(
                    By.XPATH, "//table[contains(@id,'grd_csProgLst') and contains(@id,'table')]", timeout=10
                )

            # Extract table rows
            rows = progress_table.find_elements(By.XPATH, ".//tr")

            for row in rows:
                cells = row.find_elements(By.XPATH, ".//td")
                if len(cells) < 3:
                    continue

                date_text = cells[0].text.strip()
                content_text = cells[1].text.strip()
                result_text = cells[2].text.strip()

                if not date_text:
                    continue

                progress_entry = ProgressEntry(
                    date=date_text,
                    content=content_text,
                    result=result_text
                )
                progress_entries.append(progress_entry)

            self.logger.debug(f"Extracted {len(progress_entries)} progress entries for case {case_number}")

        except Exception as e:
            self.logger.warning(f"Failed to extract progress entries for case {case_number}: {e}")

        return progress_entries

    def _extract_case_number_from_text(self, text: str) -> str:
        """Extract case number from text using regex."""
        if not text:
            return ""

        match = self.CASE_NO_PATTERN.search(text)
        if match:
            return self._normalize_case_number(match.group(0))

        return ""

    def _normalize_case_number(self, case_number: str) -> str:
        """Normalize case number by removing extra whitespace."""
        if not case_number:
            return ""
        return re.sub(r'\s+', '', case_number.strip())

    def _close_case_window(self) -> None:
        """Close case detail window and return to list."""
        try:
            # If we have multiple windows, close the current one
            if len(self.webdriver_manager.driver.window_handles) > 1:
                self.webdriver_manager.driver.close()
                self.webdriver_manager.driver.switch_to.window(
                    self.webdriver_manager.driver.window_handles[0]
                )

            # Small delay to ensure window switching is complete
            time.sleep(0.5)

        except Exception as e:
            self.logger.warning(f"Error closing case window: {e}")
            # Try to switch back to main window anyway
            try:
                if self.webdriver_manager.driver.window_handles:
                    self.webdriver_manager.driver.switch_to.window(
                        self.webdriver_manager.driver.window_handles[0]
                    )
            except Exception:
                pass
