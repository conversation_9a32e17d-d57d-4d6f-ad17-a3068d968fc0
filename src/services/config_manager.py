"""Configuration management service."""

import os
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict

from ..models.config import AppConfig
from ..utils.logger import get_logger
from ..utils.exceptions import ConfigurationError


class ConfigManager(BaseSettings):
    """Configuration manager using Pydantic Settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        env_nested_delimiter="__",
        extra="ignore"
    )
    
    # Environment
    environment: str = "development"
    debug: bool = False
    timezone: str = "Asia/Seoul"
    
    # Google Cloud
    google_cloud_project: str = ""
    
    # WebDriver
    webdriver_headless: bool = True
    webdriver_window_width: int = 1920
    webdriver_window_height: int = 1080
    webdriver_timeout_seconds: int = 30
    
    # Crawlers
    public_defender_base_url: str = "https://guksun.scourt.go.kr/pkj/index.on"
    public_defender_date_range_days: int = 7
    public_defender_max_retries: int = 3
    
    electronic_litigation_base_url: str = "https://ecfs.scourt.go.kr/psp/index.on?m=PSP101M01"
    electronic_litigation_max_retries: int = 3
    
    # Google Sheets
    google_sheets_batch_size: int = 500
    google_sheets_retry_attempts: int = 5
    
    # Secret Manager
    secret_manager_lawyer_accounts: str = "lawyer-accounts"
    secret_manager_electronic_litigation: str = "electronic-litigation-credentials"
    secret_manager_google_sheets: str = "google-sheets-credentials"
    secret_manager_api_key: Optional[str] = None
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    
    # FastAPI
    api_host: str = "0.0.0.0"
    api_port: int = 8080
    api_key: Optional[str] = None


class ConfigService:
    """Service for managing application configuration."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self._config: Optional[AppConfig] = None
        self._config_manager: Optional[ConfigManager] = None
    
    def load_config(self) -> AppConfig:
        """
        Load and validate application configuration.
        
        Returns:
            AppConfig: Validated application configuration
            
        Raises:
            ConfigurationError: If configuration is invalid
        """
        try:
            # Load raw configuration
            self._config_manager = ConfigManager()
            
            # Validate required fields
            self._validate_required_config()
            
            # Build structured configuration
            self._config = self._build_app_config()
            print('load config 점검중', self._config.logging.level, type(self._config.logging.level))
            self.logger.info(
                "Configuration loaded successfully",
                extra={
                    "environment": self._config.environment,
                    "debug": self._config.debug,
                    "project_id": self._config.secret_manager.project_id
                }
            )
            
            return self._config
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            raise ConfigurationError(f"Configuration loading failed: {e}") from e
    
    def get_config(self) -> AppConfig:
        """
        Get current application configuration.
        
        Returns:
            AppConfig: Current configuration
            
        Raises:
            ConfigurationError: If configuration not loaded
        """
        if self._config is None:
            raise ConfigurationError("Configuration not loaded. Call load_config() first.")
        return self._config
    
    def _validate_required_config(self) -> None:
        """Validate required configuration fields."""
        if not self._config_manager.google_cloud_project:
            raise ConfigurationError("GOOGLE_CLOUD_PROJECT environment variable is required")
    
    def _build_app_config(self) -> AppConfig:
        """Build structured AppConfig from raw configuration."""
        from ..models.config import (
            WebDriverConfig, PublicDefenderConfig, ElectronicLitigationConfig,
            GoogleSheetsConfig, SecretManagerConfig, LoggingConfig, LogLevel
        )

        # Get API key from Secret Manager if configured
        api_key = self._get_api_key_from_secret_manager()

        return AppConfig(
            environment=self._config_manager.environment,
            debug=self._config_manager.debug,
            timezone=self._config_manager.timezone,
            webdriver=WebDriverConfig(
                headless=self._config_manager.webdriver_headless,
                window_width=self._config_manager.webdriver_window_width,
                window_height=self._config_manager.webdriver_window_height,
                timeout_seconds=self._config_manager.webdriver_timeout_seconds,
            ),
            public_defender=PublicDefenderConfig(
                base_url=self._config_manager.public_defender_base_url,
                default_date_range_days=self._config_manager.public_defender_date_range_days,
                max_retries=self._config_manager.public_defender_max_retries,
            ),
            electronic_litigation=ElectronicLitigationConfig(
                base_url=self._config_manager.electronic_litigation_base_url,
                max_retries=self._config_manager.electronic_litigation_max_retries,
            ),
            google_sheets=GoogleSheetsConfig(
                batch_size=self._config_manager.google_sheets_batch_size,
                retry_attempts=self._config_manager.google_sheets_retry_attempts,
            ),
            secret_manager=SecretManagerConfig(
                project_id=self._config_manager.google_cloud_project,
                lawyer_accounts_secret=self._config_manager.secret_manager_lawyer_accounts,
                electronic_litigation_secret=self._config_manager.secret_manager_electronic_litigation,
                google_sheets_secret=self._config_manager.secret_manager_google_sheets,
            ),
            logging=LoggingConfig(
                level=LogLevel(self._config_manager.log_level.upper()),
                format=self._config_manager.log_format,
            ),
            flask_host=self._config_manager.api_host,
            flask_port=self._config_manager.api_port,
            api_key=api_key,
        )

    def _get_api_key_from_secret_manager(self) -> Optional[str]:
        """
        Get API key from Secret Manager if configured, otherwise return None.

        Returns:
            Optional[str]: API key from Secret Manager or None if not configured
        """
        # If SECRET_MANAGER_API_KEY is not configured, return None (no API key required)
        if not self._config_manager.secret_manager_api_key:
            self.logger.info("No SECRET_MANAGER_API_KEY configured, API will run without authentication")
            return None

        try:
            from .secret_manager import SecretManagerService
            from ..models.config import SecretManagerConfig

            # Create Secret Manager config
            secret_config = SecretManagerConfig(
                project_id=self._config_manager.google_cloud_project,
                lawyer_accounts_secret=self._config_manager.secret_manager_lawyer_accounts,
                electronic_litigation_secret=self._config_manager.secret_manager_electronic_litigation,
                google_sheets_secret=self._config_manager.secret_manager_google_sheets,
            )

            # Get API key from Secret Manager
            secret_manager = SecretManagerService(secret_config)
            api_key = secret_manager.get_api_key(self._config_manager.secret_manager_api_key)

            self.logger.info("Successfully retrieved API key from Secret Manager")
            return api_key

        except Exception as e:
            self.logger.warning(
                f"Failed to retrieve API key from Secret Manager: {e}. "
                "API will run without authentication."
            )
            return None


# Global configuration service instance
_config_service = ConfigService()


def get_config() -> AppConfig:
    """Get application configuration."""
    return _config_service.get_config()


def load_config() -> AppConfig:
    """Load application configuration."""
    return _config_service.load_config()
