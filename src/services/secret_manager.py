"""Google Cloud Secret Manager service."""

import json
from typing import Dict, List, Any, Optional
from google.cloud import secretmanager
from google.api_core import exceptions as gcp_exceptions

from ..models.config import SecretManagerConfig, LawyerAccount, ElectronicLitigationCredentials
from ..utils.logger import get_logger
from ..utils.exceptions import SecretManagerError


class SecretManagerService:
    """Service for managing secrets in Google Cloud Secret Manager."""
    
    def __init__(self, config: SecretManagerConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self._client: Optional[secretmanager.SecretManagerServiceClient] = None
    
    @property
    def client(self) -> secretmanager.SecretManagerServiceClient:
        """Get Secret Manager client (lazy initialization)."""
        if self._client is None:
            try:
                self._client = secretmanager.SecretManagerServiceClient()
                self.logger.info("Secret Manager client initialized")
            except Exception as e:
                self.logger.error(f"Failed to initialize Secret Manager client: {e}")
                raise SecretManagerError(f"Failed to initialize Secret Manager client: {e}") from e
        return self._client
    
    def get_lawyer_accounts(self) -> List[LawyerAccount]:
        """
        Get lawyer account credentials from Secret Manager.
        
        Returns:
            List[LawyerAccount]: List of lawyer accounts
            
        Raises:
            SecretManagerError: If secret retrieval fails
        """
        try:
            secret_data = self._get_secret(self.config.lawyer_accounts_secret)
            accounts_data = json.loads(secret_data)
            
            accounts = []
            for account_data in accounts_data.get("accounts", []):
                account = LawyerAccount(
                    username=account_data["username"],
                    password=account_data["password"],
                    assigned_courts=account_data.get("assigned_courts", []),
                    active=account_data.get("active", True)
                )
                accounts.append(account)
            
            self.logger.info(f"Retrieved {len(accounts)} lawyer accounts")
            return accounts
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse lawyer accounts JSON: {e}")
            raise SecretManagerError(f"Invalid JSON in lawyer accounts secret: {e}") from e
        except Exception as e:
            self.logger.error(f"Failed to get lawyer accounts: {e}")
            raise SecretManagerError(f"Failed to get lawyer accounts: {e}") from e
    
    def get_electronic_litigation_credentials(self) -> ElectronicLitigationCredentials:
        """
        Get electronic litigation credentials from Secret Manager.
        
        Returns:
            ElectronicLitigationCredentials: Electronic litigation credentials
            
        Raises:
            SecretManagerError: If secret retrieval fails
        """
        try:
            secret_data = self._get_secret(self.config.electronic_litigation_secret)
            creds_data = json.loads(secret_data)
            
            credentials = ElectronicLitigationCredentials(
                username=creds_data["username"],
                password=creds_data["password"]
            )
            
            self.logger.info("Retrieved electronic litigation credentials")
            return credentials
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse electronic litigation credentials JSON: {e}")
            raise SecretManagerError(f"Invalid JSON in electronic litigation credentials secret: {e}") from e
        except Exception as e:
            self.logger.error(f"Failed to get electronic litigation credentials: {e}")
            raise SecretManagerError(f"Failed to get electronic litigation credentials: {e}") from e
    
    def get_google_sheets_credentials(self) -> Dict[str, Any]:
        """
        Get Google Sheets service account credentials from Secret Manager.
        
        Returns:
            Dict[str, Any]: Google Sheets service account credentials
            
        Raises:
            SecretManagerError: If secret retrieval fails
        """
        try:
            secret_data = self._get_secret(self.config.google_sheets_secret)
            credentials = json.loads(secret_data)
            
            # Validate required fields
            required_fields = ["type", "project_id", "private_key", "client_email"]
            for field in required_fields:
                if field not in credentials:
                    raise SecretManagerError(f"Missing required field '{field}' in Google Sheets credentials")
            
            self.logger.info("Retrieved Google Sheets credentials")
            return credentials
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse Google Sheets credentials JSON: {e}")
            raise SecretManagerError(f"Invalid JSON in Google Sheets credentials secret: {e}") from e
        except Exception as e:
            self.logger.error(f"Failed to get Google Sheets credentials: {e}")
            raise SecretManagerError(f"Failed to get Google Sheets credentials: {e}") from e

    def get_api_key(self, secret_name: str) -> str:
        """
        Get API key from Secret Manager.

        Args:
            secret_name: Name of the secret containing the API key

        Returns:
            str: API key value

        Raises:
            SecretManagerError: If API key retrieval fails
        """
        try:
            return self._get_secret(secret_name)
        except Exception as e:
            self.logger.error(f"Failed to get API key from secret {secret_name}: {e}")
            raise SecretManagerError(f"Failed to get API key from secret {secret_name}: {e}") from e

    def _get_secret(self, secret_name: str, version: str = "latest") -> str:
        """
        Get secret value from Secret Manager.
        
        Args:
            secret_name: Name of the secret
            version: Version of the secret (default: "latest")
            
        Returns:
            str: Secret value
            
        Raises:
            SecretManagerError: If secret retrieval fails
        """
        try:
            secret_path = self.client.secret_version_path(
                self.config.project_id, secret_name, version
            )
            
            response = self.client.access_secret_version(request={"name": secret_path})
            secret_value = response.payload.data.decode("UTF-8")
            
            self.logger.debug(f"Retrieved secret: {secret_name}")
            return secret_value
            
        except gcp_exceptions.NotFound:
            self.logger.error(f"Secret not found: {secret_name}")
            raise SecretManagerError(f"Secret not found: {secret_name}")
        except gcp_exceptions.PermissionDenied:
            self.logger.error(f"Permission denied accessing secret: {secret_name}")
            raise SecretManagerError(f"Permission denied accessing secret: {secret_name}")
        except Exception as e:
            self.logger.error(f"Failed to get secret {secret_name}: {e}")
            raise SecretManagerError(f"Failed to get secret {secret_name}: {e}") from e
    
    def create_or_update_secret(self, secret_name: str, secret_value: str) -> None:
        """
        Create or update a secret in Secret Manager.
        
        Args:
            secret_name: Name of the secret
            secret_value: Value of the secret
            
        Raises:
            SecretManagerError: If secret creation/update fails
        """
        try:
            parent = f"projects/{self.config.project_id}"
            
            # Try to create the secret first
            try:
                secret = self.client.create_secret(
                    request={
                        "parent": parent,
                        "secret_id": secret_name,
                        "secret": {"replication": {"automatic": {}}},
                    }
                )
                self.logger.info(f"Created secret: {secret_name}")
            except gcp_exceptions.AlreadyExists:
                # Secret already exists, we'll add a new version
                self.logger.debug(f"Secret already exists: {secret_name}")
            
            # Add secret version
            secret_path = self.client.secret_path(self.config.project_id, secret_name)
            response = self.client.add_secret_version(
                request={
                    "parent": secret_path,
                    "payload": {"data": secret_value.encode("UTF-8")},
                }
            )
            
            self.logger.info(f"Added version to secret: {secret_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to create/update secret {secret_name}: {e}")
            raise SecretManagerError(f"Failed to create/update secret {secret_name}: {e}") from e
