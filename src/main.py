"""Main entry point for the court crawling system."""

import sys
import asyncio
from typing import Optional
import click
import uvicorn

from .services.config_manager import load_config
from .services.secret_manager import SecretManagerService
from .services.webdriver_manager import WebDriverManager
from .services.sheets_client import GoogleSheetsClient
from .crawlers.public_defender import PublicDefenderCrawler
from .crawlers.electronic_litigation import ElectronicLitigationCrawler
from .utils.logger import configure_logging, get_logger
from .utils.date_utils import format_datetime


def setup_logging(config):
    """Setup logging configuration."""
    
    configure_logging(config.logging.level, config.logging.format)
    return get_logger("Main")


@click.group()
def cli():
    """Court Crawling System CLI."""
    pass


@cli.command()
@click.option('--days', '-d', default=7, help='Number of days to look back')
@click.option('--lawyer', '-l', help='Specific lawyer username to use')
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.option('--dryrun', is_flag=True, help='Crawling but not save.')
def crawl_public_defender(days: int, lawyer: Optional[str], debug: bool, dryrun: bool):
    """국선사건."""
    try:
        # Load configuration
        config = load_config()

        if debug:
            config.logging.level = "DEBUG"
        
        logger = setup_logging(config)
        logger.info(f"Starting public defender crawl (days: {days}, lawyer: {lawyer})")
        
        # Initialize services
        secret_manager = SecretManagerService(config.secret_manager)
        webdriver_manager = WebDriverManager(config.webdriver)
        
        # Get credentials
        lawyer_accounts = secret_manager.get_lawyer_accounts()
        if not lawyer_accounts:
            logger.error("No lawyer accounts configured")
            sys.exit(1)
        
        # Filter by specific lawyer if requested
        if lawyer:
            lawyer_accounts = [acc for acc in lawyer_accounts if acc.username == lawyer and acc.active]
            if not lawyer_accounts:
                logger.error(f"Lawyer account '{lawyer}' not found or inactive")
                sys.exit(1)
        else:
            lawyer_accounts = [acc for acc in lawyer_accounts if acc.active]
        
        # Get Google Sheets credentials
        if dryrun:
            sheets_credentials = None
            sheets_client = None
        else:
            sheets_credentials = secret_manager.get_google_sheets_credentials()
            sheets_client = GoogleSheetsClient(config.google_sheets, sheets_credentials, config.timezone)
        
        total_cases = 0
        
        # Crawl for each lawyer account
        for lawyer_account in lawyer_accounts:
            try:
                logger.info(f"Crawling for lawyer: {lawyer_account.username}")
                
                with webdriver_manager:
                    crawler = PublicDefenderCrawler(config.public_defender, webdriver_manager)
                    
                    # Authenticate
                    if not crawler.authenticate(lawyer_account):
                        logger.error(f"Authentication failed for {lawyer_account.username}")
                        continue
                    
                    # Crawl cases
                    result = crawler.crawl_cases(date_range_days=days)
                    
                    if result.success and result.cases:
                        # Convert cases to dictionaries
                        case_dicts = [case.to_dict() for case in result.cases]
                        
                        # Upload to Google Sheets
                        if not dryrun:
                            sheets_client.upload_public_defender_data(case_dicts)
                        if debug:
                            from pprint import pformat
                            print('--------------------------------')
                            print('case_dicts', pformat(case_dicts))
                            print('--------------------------------')
                        total_cases += len(result.cases)
                        logger.info(f"Processed {len(result.cases)} cases for {lawyer_account.username}")
                    else:
                        logger.warning(f"No cases found for {lawyer_account.username}: {result.error_message}")
                        
            except Exception as e:
                logger.error(f"Error processing lawyer {lawyer_account.username}: {e}")
                continue
        
        logger.info(f"Public defender crawl completed. Total cases: {total_cases}")
        
    except Exception as e:
        logger.error(f"Public defender crawl failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.option('--dryrun', is_flag=True, help='Crawling but not save.')
def crawl_electronic_litigation(debug: bool, dryrun: bool):
    """Crawl electronic litigation cases."""
    # Load configuration
    config = load_config()
    if debug:
        config.logging.level = "DEBUG"
    
    logger = setup_logging(config)
    logger.info("Starting electronic litigation crawl")
    try:        
        # Initialize services
        secret_manager = SecretManagerService(config.secret_manager)
        webdriver_manager = WebDriverManager(config.webdriver)
        
        # Get credentials
        el_credentials = secret_manager.get_electronic_litigation_credentials()
        if dryrun:
            sheets_credentials = None
            sheets_client = None
        else:
            sheets_credentials = secret_manager.get_google_sheets_credentials()
            sheets_client = GoogleSheetsClient(config.google_sheets, sheets_credentials, config.timezone)
        
        with webdriver_manager:
            crawler = ElectronicLitigationCrawler(config.electronic_litigation, webdriver_manager)
            
            # Authenticate
            if not crawler.authenticate(el_credentials):
                logger.error("Electronic litigation authentication failed")
                sys.exit(1)
            
            # Crawl cases
            result = crawler.crawl_cases()
            
            if result.success and result.cases:
                # Prepare data for upload
                general_data = []
                progress_data = []
                
                for case in result.cases:
                    general_data.append(case.to_general_dict())
                    progress_data.extend(case.to_progress_dicts())
                
                # Upload to Google Sheets
                if not dryrun:
                    sheets_client.upload_electronic_litigation_data(general_data, progress_data)
                else:
                    from pprint import pformat
                    logger.debug('--------------------------------')
                    logger.debug('general_data')
                    logger.debug(pformat(general_data))
                    logger.debug('progress_data')
                    logger.debug(pformat(progress_data))
                    logger.debug('--------------------------------')
                
                logger.info(f"Electronic litigation crawl completed. Cases: {len(result.cases)}")
            else:
                logger.warning(f"No electronic litigation cases found: {result.error_message}")
    except Exception as e:
        logger.error(f"Electronic litigation crawl failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=8080, help='Port to bind to')
@click.option('--reload', is_flag=True, help='Enable auto-reload')
@click.option('--debug', is_flag=True, help='Enable debug logging')
def start_server(host: str, port: int, reload: bool, debug: bool):
    """Start the FastAPI server."""
    try:
        # Load configuration to validate it
        config = load_config()
        if debug:
            config.logging.level = "DEBUG"
        
        logger = setup_logging(config)
        logger.info(f"Starting FastAPI server on {host}:{port}")
        
        uvicorn.run(
            "src.app:app",
            host=host,
            port=port,
            reload=reload,
            log_level="debug" if debug else "info"
        )
        
    except Exception as e:
        print(f"Failed to start server: {e}")
        sys.exit(1)


# CLI entry points for pyproject.toml scripts
def crawl_public_defender_cli():
    """Entry point for public defender crawling."""
    cli(['crawl-public-defender'] + sys.argv[1:])


def crawl_electronic_litigation_cli():
    """Entry point for electronic litigation crawling."""
    cli(['crawl-electronic-litigation'] + sys.argv[1:])


def start_server_cli():
    """Entry point for starting the server."""
    cli(['start-server'] + sys.argv[1:])


if __name__ == '__main__':
    cli()
