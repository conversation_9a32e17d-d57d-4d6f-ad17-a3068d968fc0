"""Configuration models."""

from pydantic import BaseModel, Field
from typing import Dict, List, Optional
from enum import Enum


class LogLevel(str, Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class WebDriverConfig(BaseModel):
    """WebDriver configuration."""
    headless: bool = True
    window_width: int = 1920
    window_height: int = 1080
    user_agent: str = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    timeout_seconds: int = 30
    implicit_wait_seconds: int = 10
    page_load_timeout_seconds: int = 30


class CrawlerConfig(BaseModel):
    """Base crawler configuration."""
    base_url: str
    max_retries: int = 3
    timeout_seconds: int = 30
    retry_delay_seconds: int = 1


class PublicDefenderConfig(CrawlerConfig):
    """Public defender crawler configuration."""
    base_url: str = "https://guksun.scourt.go.kr/pkj/index.on"
    default_date_range_days: int = 7
    max_cases_per_run: int = 1000


class ElectronicLitigationConfig(CrawlerConfig):
    """Electronic litigation crawler configuration."""
    base_url: str = "https://ecfs.scourt.go.kr/psp/index.on?m=PSP101M01"
    max_cases_per_run: int = 100


class GoogleSheetsConfig(BaseModel):
    """Google Sheets configuration."""
    batch_size: int = 500
    retry_attempts: int = 5
    retry_delay_seconds: int = 1
    max_retry_delay_seconds: int = 16


class SecretManagerConfig(BaseModel):
    """Secret Manager configuration."""
    project_id: str
    lawyer_accounts_secret: str = "lawyer-accounts"
    electronic_litigation_secret: str = "electronic-litigation-credentials"
    google_sheets_secret: str = "google-sheets-credentials"


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: LogLevel = LogLevel.INFO
    format: str = "json"
    correlation_id_header: str = "X-Correlation-ID"


class AppConfig(BaseModel):
    """Main application configuration."""
    environment: str = Field(default="development")
    debug: bool = Field(default=False)
    timezone: str = Field(default="Asia/Seoul")
    
    # Service configurations
    webdriver: WebDriverConfig = Field(default_factory=WebDriverConfig)
    public_defender: PublicDefenderConfig = Field(default_factory=PublicDefenderConfig)
    electronic_litigation: ElectronicLitigationConfig = Field(default_factory=ElectronicLitigationConfig)
    google_sheets: GoogleSheetsConfig = Field(default_factory=GoogleSheetsConfig)
    secret_manager: SecretManagerConfig
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # Flask configuration
    flask_host: str = "0.0.0.0"
    flask_port: int = 8080
    api_key: Optional[str] = None


class LawyerAccount(BaseModel):
    """Lawyer account information."""
    username: str
    password: str
    assigned_courts: List[str] = Field(default_factory=list)
    active: bool = True


class ElectronicLitigationCredentials(BaseModel):
    """Electronic litigation credentials."""
    username: str
    password: str
