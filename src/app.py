"""FastAPI application for webhook triggers."""

import uuid
from typing import Op<PERSON>, Dict, Any
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Security, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from .services.config_manager import load_config, get_config
from .services.secret_manager import SecretManagerService
from .services.webdriver_manager import WebDriverManager
from .services.sheets_client import GoogleSheetsClient
from .crawlers.public_defender import PublicDefenderCrawler
from .crawlers.electronic_litigation import ElectronicLitigationCrawler
from .utils.logger import configure_logging, get_logger, set_correlation_id
from .utils.date_utils import get_utc_now


# Request/Response models
class CrawlRequest(BaseModel):
    """Base crawl request model."""
    date_range_days: Optional[int] = None
    correlation_id: Optional[str] = None


class PublicDefenderCrawlRequest(CrawlRequest):
    """Public defender crawl request."""
    lawyer_username: Optional[str] = None  # If specified, use only this lawyer account


class CrawlResponse(BaseModel):
    """Crawl response model."""
    success: bool
    message: str
    case_count: int = 0
    correlation_id: str
    timestamp: datetime
    error_details: Optional[str] = None


class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    timestamp: datetime
    version: str = "0.1.0"


# Initialize FastAPI app
app = FastAPI(
    title="Court Crawling System",
    description="Scalable court case crawling system for Google Cloud Run",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer(auto_error=False)

# Global variables
logger = None
config = None


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    global logger, config
    
    try:
        # Load configuration
        config = load_config()
        
        # Configure logging
        configure_logging(config.logging.level.value, config.logging.format)
        logger = get_logger("FastAPI")
        
        logger.info("Court Crawling System started", extra={"version": "0.1.0"})
        
    except Exception as e:
        print(f"Failed to initialize application: {e}")
        raise


def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)) -> bool:
    """Verify API key if configured."""
    if not config.api_key:
        return True  # No API key required
    
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required"
        )
    
    if credentials.credentials != config.api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    
    return True


def get_correlation_id(request: CrawlRequest) -> str:
    """Get or generate correlation ID."""
    correlation_id = request.correlation_id or str(uuid.uuid4())
    set_correlation_id(correlation_id)
    return correlation_id


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        timestamp=get_utc_now()
    )


@app.post("/crawl/public-defender", response_model=CrawlResponse)
async def crawl_public_defender(
    request: PublicDefenderCrawlRequest,
    _: bool = Depends(verify_api_key)
):
    """Trigger public defender crawling."""
    correlation_id = get_correlation_id(request)
    
    try:
        logger.info("Starting public defender crawl", extra={"correlation_id": correlation_id})
        
        # Initialize services
        secret_manager = SecretManagerService(config.secret_manager)
        webdriver_manager = WebDriverManager(config.webdriver)
        
        # Get credentials
        lawyer_accounts = secret_manager.get_lawyer_accounts()
        if not lawyer_accounts:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No lawyer accounts configured"
            )
        
        # Filter by specific lawyer if requested
        if request.lawyer_username:
            lawyer_accounts = [
                acc for acc in lawyer_accounts 
                if acc.username == request.lawyer_username and acc.active
            ]
            if not lawyer_accounts:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Lawyer account '{request.lawyer_username}' not found or inactive"
                )
        else:
            lawyer_accounts = [acc for acc in lawyer_accounts if acc.active]
        
        # Get Google Sheets credentials
        sheets_credentials = secret_manager.get_google_sheets_credentials()
        sheets_client = GoogleSheetsClient(config.google_sheets, sheets_credentials, config.timezone)
        
        total_cases = 0
        
        # Crawl for each lawyer account
        for lawyer_account in lawyer_accounts:
            try:
                logger.info(f"Crawling for lawyer: {lawyer_account.username}")
                
                with webdriver_manager:
                    crawler = PublicDefenderCrawler(config.public_defender, webdriver_manager)
                    
                    # Authenticate
                    if not crawler.authenticate(lawyer_account):
                        logger.error(f"Authentication failed for {lawyer_account.username}")
                        continue
                    
                    # Crawl cases
                    result = crawler.crawl_cases(date_range_days=request.date_range_days)
                    
                    if result.success and result.cases:
                        # Convert cases to dictionaries
                        case_dicts = [case.to_dict() for case in result.cases]
                        
                        # Upload to Google Sheets
                        sheets_client.upload_public_defender_data(case_dicts)
                        
                        total_cases += len(result.cases)
                        logger.info(f"Processed {len(result.cases)} cases for {lawyer_account.username}")
                    else:
                        logger.warning(f"No cases found for {lawyer_account.username}: {result.error_message}")
                        
            except Exception as e:
                logger.error(f"Error processing lawyer {lawyer_account.username}: {e}")
                continue
        
        return CrawlResponse(
            success=True,
            message=f"Successfully crawled {total_cases} public defender cases",
            case_count=total_cases,
            correlation_id=correlation_id,
            timestamp=get_utc_now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Public defender crawl failed: {e}", extra={"correlation_id": correlation_id})
        return CrawlResponse(
            success=False,
            message="Public defender crawl failed",
            correlation_id=correlation_id,
            timestamp=get_utc_now(),
            error_details=str(e)
        )


@app.post("/crawl/electronic-litigation", response_model=CrawlResponse)
async def crawl_electronic_litigation(
    request: CrawlRequest,
    _: bool = Depends(verify_api_key)
):
    """Trigger electronic litigation crawling."""
    correlation_id = get_correlation_id(request)
    
    try:
        logger.info("Starting electronic litigation crawl", extra={"correlation_id": correlation_id})
        
        # Initialize services
        secret_manager = SecretManagerService(config.secret_manager)
        webdriver_manager = WebDriverManager(config.webdriver)
        
        # Get credentials
        el_credentials = secret_manager.get_electronic_litigation_credentials()
        sheets_credentials = secret_manager.get_google_sheets_credentials()
        sheets_client = GoogleSheetsClient(config.google_sheets, sheets_credentials, config.timezone)
        
        with webdriver_manager:
            crawler = ElectronicLitigationCrawler(config.electronic_litigation, webdriver_manager)
            
            # Authenticate
            if not crawler.authenticate(el_credentials):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Electronic litigation authentication failed"
                )
            
            # Crawl cases
            result = crawler.crawl_cases()
            
            if result.success and result.cases:
                # Prepare data for upload
                general_data = []
                progress_data = []
                
                for case in result.cases:
                    general_data.append(case.to_general_dict())
                    progress_data.extend(case.to_progress_dicts())
                
                # Upload to Google Sheets
                sheets_client.upload_electronic_litigation_data(general_data, progress_data)
                
                return CrawlResponse(
                    success=True,
                    message=f"Successfully crawled {len(result.cases)} electronic litigation cases",
                    case_count=len(result.cases),
                    correlation_id=correlation_id,
                    timestamp=get_utc_now()
                )
            else:
                return CrawlResponse(
                    success=False,
                    message="No electronic litigation cases found",
                    correlation_id=correlation_id,
                    timestamp=get_utc_now(),
                    error_details=result.error_message
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Electronic litigation crawl failed: {e}", extra={"correlation_id": correlation_id})
        return CrawlResponse(
            success=False,
            message="Electronic litigation crawl failed",
            correlation_id=correlation_id,
            timestamp=get_utc_now(),
            error_details=str(e)
        )


@app.post("/crawl/all", response_model=CrawlResponse)
async def crawl_all(
    request: PublicDefenderCrawlRequest,
    _: bool = Depends(verify_api_key)
):
    """Trigger both public defender and electronic litigation crawling."""
    correlation_id = get_correlation_id(request)

    try:
        logger.info("Starting combined crawl", extra={"correlation_id": correlation_id})

        total_cases = 0
        errors = []

        # Run public defender crawl
        try:
            pd_response = await crawl_public_defender(request)
            if pd_response.success:
                total_cases += pd_response.case_count
            else:
                errors.append(f"Public defender: {pd_response.error_details}")
        except Exception as e:
            errors.append(f"Public defender: {str(e)}")

        # Run electronic litigation crawl
        try:
            el_request = CrawlRequest(correlation_id=correlation_id)
            el_response = await crawl_electronic_litigation(el_request)
            if el_response.success:
                total_cases += el_response.case_count
            else:
                errors.append(f"Electronic litigation: {el_response.error_details}")
        except Exception as e:
            errors.append(f"Electronic litigation: {str(e)}")

        if errors:
            return CrawlResponse(
                success=len(errors) < 2,  # Success if at least one crawler worked
                message=f"Combined crawl completed with {len(errors)} errors. Total cases: {total_cases}",
                case_count=total_cases,
                correlation_id=correlation_id,
                timestamp=get_utc_now(),
                error_details="; ".join(errors)
            )
        else:
            return CrawlResponse(
                success=True,
                message=f"Successfully completed combined crawl. Total cases: {total_cases}",
                case_count=total_cases,
                correlation_id=correlation_id,
                timestamp=get_utc_now()
            )

    except Exception as e:
        logger.error(f"Combined crawl failed: {e}", extra={"correlation_id": correlation_id})
        return CrawlResponse(
            success=False,
            message="Combined crawl failed",
            correlation_id=correlation_id,
            timestamp=get_utc_now(),
            error_details=str(e)
        )


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    return app


if __name__ == "__main__":
    # For local development
    import os

    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8080"))

    uvicorn.run(
        "src.app:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
